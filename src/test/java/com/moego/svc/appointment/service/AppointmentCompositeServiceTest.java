package com.moego.svc.appointment.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.service.appointment.v1.GetLastPetDetailRequest;
import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import com.moego.svc.appointment.dto.AppointmentPetServiceDTO;
import com.moego.svc.appointment.dto.PetDetailDTO;
import com.moego.svc.appointment.dto.SaveOrUpdatePetDetailDTO;
import com.moego.svc.appointment.service.remote.CompanyRemoteService;
import com.moego.svc.appointment.service.remote.LodgingRemoteService;
import com.moego.svc.appointment.service.remote.OfferingRemoteService;
import com.moego.svc.appointment.service.remote.OrderRemoteService;
import com.moego.svc.appointment.utils.Pair;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionOperations;

@SpringBootTest
class AppointmentCompositeServiceTest {

    @InjectMocks
    private AppointmentCompositeService appointmentCompositeService;

    @Mock
    private AppointmentServiceProxy appointmentService;

    @Mock
    private PetDetailServiceProxy petDetailService;

    @Mock
    private EvaluationServiceDetailService evaluationService;

    @Mock
    private OrderRemoteService orderRemoteService;

    @Mock
    private PricingRuleRecordApplyService pricingRuleApplyService;

    @Mock
    private ApplicationEventPublisher publisher;

    @Mock
    private LodgingRemoteService lodgingRemoteService;

    @Mock
    private OfferingRemoteService offeringRemoteService;

    @Mock
    private CompanyRemoteService companyRemoteService;

    @Mock
    private TransactionOperations transactionOperations;

    @Test
    void saveOrUpdatePetDetails() {
        // Arrange
        var appointmentId = 1L;
        SaveOrUpdatePetDetailDTO dto = new SaveOrUpdatePetDetailDTO();
        dto.setAppointmentId(appointmentId);
        dto.setTokenStaffId(2L);
        MoeGroomingAppointment beforeAppointment = new MoeGroomingAppointment();
        beforeAppointment.setId((int) appointmentId);
        beforeAppointment.setBusinessId(100);
        beforeAppointment.setCustomerId(200);
        beforeAppointment.setAppointmentDate("2024-10-21");
        beforeAppointment.setAppointmentStartTime(540);
        MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
        petDetail.setGroomingId((int) appointmentId);
        petDetail.setServiceItemType((byte) ServiceItemType.BOARDING_VALUE);
        List<MoeGroomingPetDetail> petDetails = List.of(petDetail);

        // 使其执行传入的回调函数并返回回调的结果
        when(transactionOperations.execute(any(TransactionCallback.class))).thenAnswer(invocation -> {
            TransactionCallback<Object> callback = invocation.getArgument(0);
            return callback.doInTransaction(Mockito.mock(TransactionStatus.class));
        });

        when(appointmentService.mustGet(anyLong())).thenReturn(beforeAppointment);
        when(petDetailService.getPetDetailList(anyLong())).thenReturn(petDetails);
        doNothing().when(petDetailService).upsertAllInOnePetDetail(any(), any(), any());
        doNothing().when(evaluationService).upsertPetEvaluation(any(), any());
        when(appointmentService.refreshAppointmentDateTime(any())).thenReturn(1);
        doNothing().when(pricingRuleApplyService).applyPricingRule(any(), any(), any(), any());

        MoeGroomingAppointment afterAppointment = new MoeGroomingAppointment();
        afterAppointment.setId((int) appointmentId);
        when(appointmentService.getAppointment(any(), any())).thenReturn(afterAppointment);
        doNothing().when(orderRemoteService).updateOrder(any());
        doNothing().when(publisher).publishEvent(any());

        // Act
        MoeGroomingAppointment result = appointmentCompositeService.saveOrUpdatePetDetails(dto);

        // Assert
        MoeGroomingAppointment expected = new MoeGroomingAppointment();
        expected.setId((int) appointmentId);
        assertThat(result).usingRecursiveComparison().isEqualTo(expected);
    }

    @Test
    void getLastLodgingUnitId_WhenAllDataValid_ShouldReturnMatchingPairs() {
        long companyId = 1L;
        long businessId = 2L;
        Collection<Long> customerIds = Arrays.asList(101L, 102L);
        Collection<Long> petIds = Arrays.asList(201L, 202L);
        Collection<Long> serviceIds = Arrays.asList(301L, 302L);

        // Arrange
        List<MoeGroomingPetDetail> petDetails =
                Arrays.asList(createPetDetail(201, 301, 401L), createPetDetail(202, 302, 402L));

        List<LodgingUnitModel> lodgingUnits = Arrays.asList(createLodgingUnit(401L), createLodgingUnit(402L));

        when(petDetailService.getPetLastPetDetail(
                        eq(companyId), eq(customerIds), eq(petIds), any(GetLastPetDetailRequest.Filter.class)))
                .thenReturn(petDetails);

        when(lodgingRemoteService.getLodgingUnitByUnitIds(companyId, businessId, Arrays.asList(401L, 402L)))
                .thenReturn(lodgingUnits);

        // Act
        Map<Pair<Long, Long>, Long> result = appointmentCompositeService.getLastLodgingUnitId(
                companyId, businessId, customerIds, petIds, serviceIds);

        // Assert
        assertEquals(2, result.size());
        assertEquals(401L, result.get(Pair.of(201L, 301L)));
        assertEquals(402L, result.get(Pair.of(202L, 302L)));
    }

    private MoeGroomingPetDetail createPetDetail(Integer petId, Integer serviceId, Long lodgingId) {
        MoeGroomingPetDetail detail = new MoeGroomingPetDetail();
        detail.setPetId(petId);
        detail.setServiceId(serviceId);
        detail.setLodgingId(lodgingId);
        return detail;
    }

    private LodgingUnitModel createLodgingUnit(Long id) {
        return LodgingUnitModel.newBuilder().setId(id).build();
    }

    @Test
    void testBatchAddServiceAndCheckIn_WhenHaveLastPetDetail() {
        // Given
        long companyId = 1L;
        long businessId = 2L;

        // Mock MoeGroomingAppointment
        MoeGroomingAppointment appointment = new MoeGroomingAppointment();
        appointment.setId(1);
        appointment.setCompanyId(companyId);
        appointment.setBusinessId((int) businessId);
        when(appointmentService.mustGet(anyLong())).thenReturn(appointment);

        // Mock CompanyRemoteService
        when(companyRemoteService.getTimezoneName(anyLong())).thenReturn("America/Los_Angeles");

        // Mock CustomizedServiceView
        CustomizedServiceView service =
                CustomizedServiceView.newBuilder().setId(100L).build();
        Map<Long, CustomizedServiceView> serviceMap = new HashMap<>();
        serviceMap.put(100L, service);
        Map<Long, Map<Long, CustomizedServiceView>> petServiceMap = new HashMap<>();
        petServiceMap.put(1L, serviceMap);
        when(offeringRemoteService.listService(anyLong(), anyLong(), anyMap())).thenReturn(petServiceMap);

        // Mock PetDetailDTO
        PetDetailDTO petDetailDTO = new PetDetailDTO()
                .setPetId(1L)
                .setPetDetail(new MoeGroomingPetDetail())
                .setOperations(new ArrayList<>());
        when(petDetailService.buildPetDetailDTO(any(), any(), anyLong(), any(), any()))
                .thenReturn(petDetailDTO);

        // Mock Last PetDetails
        MoeGroomingPetDetail lastPetDetail = new MoeGroomingPetDetail();
        lastPetDetail.setPetId(1);
        lastPetDetail.setServiceId(100);
        lastPetDetail.setLodgingId(1L);
        when(petDetailService.getPetLastPetDetail(anyLong(), any(), any(), any()))
                .thenReturn(List.of(lastPetDetail));

        // Mock LodgingUnits
        LodgingUnitModel lodgingUnit = LodgingUnitModel.newBuilder().setId(1L).build();
        when(lodgingRemoteService.getLodgingUnitByUnitIds(anyLong(), anyLong(), any()))
                .thenReturn(List.of(lodgingUnit));

        // Create test AppointmentPetServiceDTO
        AppointmentPetServiceDTO dto = new AppointmentPetServiceDTO();
        dto.setAppointmentId(1L);
        dto.setPetId(1L);
        dto.setServiceId(100L);
        dto.setCustomerId(10L);
        dto.setStartDate("2024-03-18");
        Set<AppointmentPetServiceDTO> appointmentPetServices = Set.of(dto);

        // When
        appointmentCompositeService.batchAddServiceAndCheckIn(companyId, businessId, appointmentPetServices);

        // Then
        assertEquals(1, appointmentPetServices.size());
    }

    @Test
    void testBatchAddServiceAndCheckIn_WhenNotHaveLastPetDetail() {
        // Given
        long companyId = 1L;
        long businessId = 2L;

        // Mock MoeGroomingAppointment
        MoeGroomingAppointment appointment = new MoeGroomingAppointment();
        appointment.setId(1);
        appointment.setCompanyId(companyId);
        appointment.setBusinessId((int) businessId);
        when(appointmentService.mustGet(anyLong())).thenReturn(appointment);

        // Mock CompanyRemoteService
        when(companyRemoteService.getTimezoneName(anyLong())).thenReturn("America/Los_Angeles");

        // Mock CustomizedServiceView
        CustomizedServiceView service =
                CustomizedServiceView.newBuilder().setId(100L).build();
        Map<Long, CustomizedServiceView> serviceMap = new HashMap<>();
        serviceMap.put(100L, service);
        Map<Long, Map<Long, CustomizedServiceView>> petServiceMap = new HashMap<>();
        petServiceMap.put(1L, serviceMap);
        when(offeringRemoteService.listService(anyLong(), anyLong(), anyMap())).thenReturn(petServiceMap);

        // Mock PetDetailDTO
        PetDetailDTO petDetailDTO = new PetDetailDTO()
                .setPetId(1L)
                .setPetDetail(new MoeGroomingPetDetail())
                .setOperations(new ArrayList<>());
        when(petDetailService.buildPetDetailDTO(any(), any(), anyLong(), any(), any()))
                .thenReturn(petDetailDTO);

        // Mock Last PetDetails
        when(petDetailService.getPetLastPetDetail(anyLong(), any(), any(), any()))
                .thenReturn(List.of());

        // Mock LodgingUnits
        when(lodgingRemoteService.getLodgingUnitByUnitIds(anyLong(), anyLong(), any()))
                .thenReturn(List.of());

        // Create test AppointmentPetServiceDTO
        AppointmentPetServiceDTO dto = new AppointmentPetServiceDTO();
        dto.setAppointmentId(1L);
        dto.setPetId(1L);
        dto.setServiceId(100L);
        dto.setCustomerId(10L);
        dto.setStartDate("2024-03-18");
        Set<AppointmentPetServiceDTO> appointmentPetServices = Set.of(dto);

        // When
        appointmentCompositeService.batchAddServiceAndCheckIn(companyId, businessId, appointmentPetServices);

        // Then
        assertEquals(1, appointmentPetServices.size());
    }
}
