package com.moego.svc.appointment.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

import com.moego.idl.models.appointment.v1.LodgingAssignAppointmentInfo;
import com.moego.idl.models.appointment.v1.LodgingAssignInfo;
import com.moego.idl.models.appointment.v1.LodgingAssignPetDetailInfo;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessPetSizeModel;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.svc.appointment.domain.EvaluationServiceDetail;
import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import com.moego.svc.appointment.service.params.BatchCheckInAutoAssignParam;
import com.moego.svc.appointment.service.remote.OrganizationRemoteService;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class LodgingServiceTest {

    @Mock
    AppointmentServiceProxy appointmentService;

    @Mock
    PetDetailServiceProxy petDetailService;

    @Mock
    EvaluationServiceDetailService petEvaluationService;

    @Mock
    OrganizationRemoteService organizationRemoteService;

    @Mock
    BoardingSplitLodgingService boardingSplitLodgingService;

    @InjectMocks
    LodgingService lodgingService;

    @Test
    void getAssignInfo() {
        LodgingService spyService = spy(lodgingService);
        doReturn(List.of()).when(appointmentService).getAppointmentsByDateRange(any(), any(), any(), any(), any());
        doReturn(List.of()).when(petEvaluationService).getPetEvaluationList(anyList());
        doReturn(0L).when(organizationRemoteService).getCompanyIdByBusinessId(any());
        assertEquals(
                0,
                spyService
                        .getAssignInfo(null, 1L, "2021-08-01", "2021-08-02", List.of())
                        .size());

        int appointmentId1 = 10;
        int appointmentId2 = 20;
        List<MoeGroomingAppointment> appointments = List.of(
                new MoeGroomingAppointment() {
                    {
                        setId(appointmentId1);
                        setCustomerId(1);
                    }
                },
                new MoeGroomingAppointment() {
                    {
                        setId(appointmentId2);
                        setCustomerId(2);
                    }
                });
        doReturn(appointments).when(appointmentService).getAppointmentsByDateRange(any(), any(), any(), any(), any());

        Long lodgingId1 = 1L;
        Long lodgingId2 = 2L;
        Long lodgingId3 = 3L;
        List<MoeGroomingPetDetail> petDetails = List.of(
                new MoeGroomingPetDetail() {
                    {
                        setId(1);
                        setLodgingId(lodgingId1);
                        setGroomingId(appointmentId1);
                        setServiceItemType(
                                Integer.valueOf(ServiceItemType.BOARDING_VALUE).byteValue());
                        setPetId(11);
                        setStartDate("2021-08-01");
                        setStartTime(540L);
                        setEndDate("2021-08-02");
                        setEndTime(540L);
                        setServiceType(ServiceType.SERVICE_VALUE);
                    }
                },
                new MoeGroomingPetDetail() {
                    {
                        setId(2);
                        setLodgingId(lodgingId2);
                        setGroomingId(appointmentId2);
                        setServiceItemType(
                                Integer.valueOf(ServiceItemType.BOARDING_VALUE).byteValue());
                        setPetId(21);
                        setStartDate("2021-08-01");
                        setStartTime(540L);
                        setEndDate("2021-08-02");
                        setEndTime(540L);
                        setServiceType(ServiceType.SERVICE_VALUE);
                    }
                },
                new MoeGroomingPetDetail() {
                    {
                        setId(3);
                        setLodgingId(lodgingId2);
                        setGroomingId(appointmentId2);
                        setServiceItemType(
                                Integer.valueOf(ServiceItemType.BOARDING_VALUE).byteValue());
                        setPetId(22);
                        setStartDate("2021-08-01");
                        setStartTime(540L);
                        setEndDate("2021-08-02");
                        setEndTime(540L);
                        setServiceType(ServiceType.SERVICE_VALUE);
                    }
                },
                new MoeGroomingPetDetail() {
                    {
                        setId(4);
                        setLodgingId(lodgingId3);
                        setGroomingId(appointmentId2);
                        setServiceItemType(
                                Integer.valueOf(ServiceItemType.BOARDING_VALUE).byteValue());
                        setPetId(31);
                        setStartDate("2021-08-01");
                        setStartTime(540L);
                        setEndDate("2021-08-02");
                        setEndTime(540L);
                        setServiceType(ServiceType.SERVICE_VALUE);
                    }
                },
                new MoeGroomingPetDetail() {
                    {
                        setId(5);
                        setLodgingId(lodgingId3);
                        setGroomingId(appointmentId1);
                        setServiceItemType(
                                Integer.valueOf(ServiceItemType.BOARDING_VALUE).byteValue());
                        setPetId(32);
                        setStartDate("2021-08-01");
                        setStartTime(540L);
                        setEndDate("2021-08-02");
                        setEndTime(540L);
                        setServiceType(ServiceType.SERVICE_VALUE);
                    }
                });

        doReturn(petDetails).when(petDetailService).getPetDetailList(anyList());
        doReturn(petDetails).when(petDetailService).getWithActualDatesInfo(anyList());
        when(boardingSplitLodgingService.getBoardingSplitLodgings(any())).thenReturn(List.of());
        var tmp = spyService.getAssignInfo(null, 1L, "2021-08-01", "2021-08-02", List.of());
        assertEquals(3, tmp.size());
        // 验证 lodging1
        assertEquals(lodgingId1, tmp.get(0).getLodgingId());
        assertEquals(1, tmp.get(0).getAppointmentsList().size());
        assertEquals(appointmentId1, tmp.get(0).getAppointmentsList().get(0).getId());
        assertEquals(
                1, tmp.get(0).getAppointmentsList().get(0).getPetDetailsList().size());
        // 验证 lodging2
        assertEquals(lodgingId2, tmp.get(1).getLodgingId());
        assertEquals(1, tmp.get(1).getAppointmentsList().size());
        assertEquals(appointmentId2, tmp.get(1).getAppointmentsList().get(0).getId());
        assertEquals(
                2, tmp.get(1).getAppointmentsList().get(0).getPetDetailsList().size());
        // 验证 lodging3
        assertEquals(lodgingId3, tmp.get(2).getLodgingId());
        assertEquals(2, tmp.get(2).getAppointmentsList().size());
        assertEquals(appointmentId2, tmp.get(2).getAppointmentsList().get(0).getId());
        assertEquals(appointmentId1, tmp.get(2).getAppointmentsList().get(1).getId());
        // 验证 lodgingId filter
        assertEquals(
                3,
                spyService
                        .getAssignInfo(null, 1L, "2021-08-01", "2021-08-02", List.of(lodgingId1, lodgingId2))
                        .size());
    }

    @Test
    void getUpcomingAppointmentReturnsEmptyMapWhenLodgingIdsIsEmpty() {
        List<Long> emptyLodgingIds = new ArrayList<>();
        Map<Long, Set<Long>> result = lodgingService.getUpcomingAppointment(1L, 1L, emptyLodgingIds);

        assertThat(result).isEmpty();
    }

    @Test
    void getUpcomingAppointmentReturnsCorrectMapWhenLodgingIdsIsNotEmpty() {
        List<Long> lodgingIds = List.of(1L, 2L);
        List<Long> upcomingAppointmentIds = List.of(1L, 2L, 3L);

        LodgingService spyService = spy(lodgingService);

        when(spyService.listUpcomingAppointment(anyLong(), anyLong())).thenReturn(upcomingAppointmentIds);

        MoeGroomingPetDetail petDetail1 = new MoeGroomingPetDetail();
        petDetail1.setLodgingId(1L);
        petDetail1.setGroomingId(1);

        MoeGroomingPetDetail petDetail2 = new MoeGroomingPetDetail();
        petDetail2.setLodgingId(2L);
        petDetail2.setGroomingId(2);

        when(petDetailService.getPetDetailList(anyList())).thenReturn(List.of(petDetail1, petDetail2));

        EvaluationServiceDetail evaluationServiceDetail = new EvaluationServiceDetail();
        evaluationServiceDetail.setLodgingId(1L);
        evaluationServiceDetail.setAppointmentId(3L);

        when(petEvaluationService.getPetEvaluationList(anyList())).thenReturn(List.of(evaluationServiceDetail));

        Map<Long, Set<Long>> result = spyService.getUpcomingAppointment(1L, 1L, lodgingIds);

        assertThat(result).hasSize(2);
        assertThat(result.get(1L)).containsAll(Set.of(1L, 3L));
        assertThat(result.get(2L)).contains(2L);
    }

    @Test
    void testBatchCheckInAutoAssignLodging() {
        LodgingTypeModel lodgingType1 = LodgingTypeModel.newBuilder()
                .setId(1L)
                .setMaxPetNum(2)
                .addAllPetSizeIds(List.of(1L, 2L))
                .build();
        LodgingTypeModel lodgingType2 = LodgingTypeModel.newBuilder()
                .setId(2L)
                .setMaxPetNum(1)
                .addAllPetSizeIds(List.of(1L))
                .build();

        LodgingUnitModel unit1 =
                LodgingUnitModel.newBuilder().setId(101L).setLodgingTypeId(1L).build();
        LodgingUnitModel unit2 =
                LodgingUnitModel.newBuilder().setId(102L).setLodgingTypeId(2L).build();

        BusinessCustomerPetInfoModel pet1 = BusinessCustomerPetInfoModel.newBuilder()
                .setId(1001L)
                .setWeight("5.0")
                .build();
        BusinessCustomerPetInfoModel pet2 = BusinessCustomerPetInfoModel.newBuilder()
                .setId(1002L)
                .setWeight("3.0")
                .build();

        BusinessPetSizeModel size1 = BusinessPetSizeModel.newBuilder()
                .setId(1L)
                .setWeightLow(0)
                .setWeightHigh(5)
                .build();
        BusinessPetSizeModel size2 = BusinessPetSizeModel.newBuilder()
                .setId(2L)
                .setWeightLow(6)
                .setWeightHigh(10)
                .build();

        // 5. 创建服务信息
        ServiceBriefView serviceBrief =
                ServiceBriefView.newBuilder().setLodgingFilter(false).build();

        // 构造入参
        BatchCheckInAutoAssignParam param = BatchCheckInAutoAssignParam.builder()
                .petIds(List.of(1001L, 1002L))
                .serviceBrief(serviceBrief)
                .date("2024-01-01")
                .lodgingTypeList(List.of(lodgingType1, lodgingType2))
                .lodgingUnitList(List.of(unit1, unit2))
                .petSizeList(List.of(size1, size2))
                .petMap(Map.of(
                        1001L, pet1,
                        1002L, pet2))
                .assignInfoList(List.of())
                .build();

        Map<Long, Long> result = LodgingService.batchCheckInAutoAssignLodging(param);

        // 验证结果
        assertThat(result).hasSize(2);
        assertThat(result.get(1001L)).isEqualTo(101L);
        assertThat(result.get(1002L)).isEqualTo(102L);
    }

    @Test
    void testBatchCheckInAutoAssignLodgingWithExistingAssignments() {
        LodgingTypeModel lodgingType1 = LodgingTypeModel.newBuilder()
                .setId(1L)
                .setMaxPetNum(2)
                .setPetSizeFilter(true)
                .addAllPetSizeIds(List.of(1L, 2L))
                .build();
        LodgingTypeModel lodgingType2 = LodgingTypeModel.newBuilder()
                .setId(2L)
                .setMaxPetNum(1)
                .setPetSizeFilter(true)
                .addAllPetSizeIds(List.of(1L))
                .build();

        LodgingUnitModel unit1 =
                LodgingUnitModel.newBuilder().setId(101L).setLodgingTypeId(1L).build();
        LodgingUnitModel unit2 =
                LodgingUnitModel.newBuilder().setId(102L).setLodgingTypeId(2L).build();

        BusinessCustomerPetInfoModel pet1 = BusinessCustomerPetInfoModel.newBuilder()
                .setId(1001L)
                .setWeight("4.0") // 尺寸1范围内
                .build();
        BusinessCustomerPetInfoModel pet2 = BusinessCustomerPetInfoModel.newBuilder()
                .setId(1002L)
                .setWeight("15.0") // 超出所有尺寸范围
                .build();
        BusinessCustomerPetInfoModel pet3 = BusinessCustomerPetInfoModel.newBuilder()
                .setId(1003L)
                .setWeight("3.0") // 尺寸1范围内
                .build();

        BusinessPetSizeModel size1 = BusinessPetSizeModel.newBuilder()
                .setId(1L)
                .setWeightLow(0)
                .setWeightHigh(5)
                .build();
        BusinessPetSizeModel size2 = BusinessPetSizeModel.newBuilder()
                .setId(2L)
                .setWeightLow(6)
                .setWeightHigh(10)
                .build();

        ServiceBriefView serviceBrief =
                ServiceBriefView.newBuilder().setLodgingFilter(false).build();

        LodgingAssignPetDetailInfo existingPetDetail = LodgingAssignPetDetailInfo.newBuilder()
                .setPetId(999)
                .setStartDate("2024-01-01")
                .setEndDate("2024-01-01")
                .build();

        LodgingAssignAppointmentInfo appointmentInfo = LodgingAssignAppointmentInfo.newBuilder()
                .addPetDetails(existingPetDetail)
                .build();

        LodgingAssignInfo assignInfo = LodgingAssignInfo.newBuilder()
                .setLodgingId(101L)
                .addAppointments(appointmentInfo)
                .build();

        BatchCheckInAutoAssignParam param = BatchCheckInAutoAssignParam.builder()
                .petIds(List.of(1001L, 1002L, 1003L))
                .serviceBrief(serviceBrief)
                .date("2024-01-01")
                .lodgingTypeList(List.of(lodgingType1, lodgingType2))
                .lodgingUnitList(List.of(unit1, unit2))
                .petSizeList(List.of(size1, size2))
                .petMap(Map.of(
                        1001L, pet1,
                        1002L, pet2,
                        1003L, pet3))
                .assignInfoList(List.of(assignInfo))
                .build();

        Map<Long, Long> result = LodgingService.batchCheckInAutoAssignLodging(param);

        // 验证结果
        assertThat(result)
                .hasSize(2) // 有2只宠物被成功分配（体重超范围的宠物未被分配）
                .containsEntry(1001L, 102L) // pet1分配到unit2（unit2优先级更高，且还有1个空位）
                .containsEntry(1003L, 101L) // pet3 分配到 unit1（unit2 已满，分配到 unit1）
                .doesNotContainKey(1002L); // pet2 size 不符合
    }

    @Test
    void testBatchCheckInAutoAssignLodgingWithEmptyInput() {
        BatchCheckInAutoAssignParam param = BatchCheckInAutoAssignParam.builder()
                .petIds(Collections.emptyList())
                .date("2024-01-01")
                .build();

        Map<Long, Long> result = LodgingService.batchCheckInAutoAssignLodging(param);
        assertThat(result).isEmpty();
    }
}
