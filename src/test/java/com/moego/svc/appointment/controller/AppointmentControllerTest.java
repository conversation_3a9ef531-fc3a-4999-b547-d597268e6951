package com.moego.svc.appointment.controller;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentCaptor.forClass;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.google.protobuf.Timestamp;
import com.google.type.Interval;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.AppointmentNoShowStatus;
import com.moego.idl.models.appointment.v1.AppointmentSource;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.appointment.v1.AppointmentUpdatedBy;
import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.idl.models.fulfillment.v1.LineItem;
import com.moego.idl.models.fulfillment.v1.ServiceItem;
import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServicePriceUnit;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.models.offering.v2.PricingRule;
import com.moego.idl.models.offering.v2.RuleType;
import com.moego.idl.models.organization.v1.StaffModel;
import com.moego.idl.models.organization.v1.TaxRuleModel;
import com.moego.idl.models.organization.v1.TimeRangeDef;
import com.moego.idl.models.organization.v1.WorkingHoursDef;
import com.moego.idl.service.appointment.v1.AppointmentList;
import com.moego.idl.service.appointment.v1.BatchBookAgainAppointmentRequest;
import com.moego.idl.service.appointment.v1.BatchBookAgainAppointmentResponse;
import com.moego.idl.service.appointment.v1.BatchCancelAppointmentRequest;
import com.moego.idl.service.appointment.v1.BatchCancelAppointmentResponse;
import com.moego.idl.service.appointment.v1.BatchQuickCheckInRequest;
import com.moego.idl.service.appointment.v1.BatchQuickCheckInResponse;
import com.moego.idl.service.appointment.v1.GetTimeOverlapAppointmentListRequest;
import com.moego.idl.service.appointment.v1.GetTimeOverlapAppointmentListResponse;
import com.moego.lib.common.proto.MoneyUtils;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.svc.appointment.converter.TimeConverter;
import com.moego.svc.appointment.domain.BoardingSplitLodging;
import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import com.moego.svc.appointment.domain.MoeGroomingServiceOperation;
import com.moego.svc.appointment.domain.PricingRuleRecordApplyLog;
import com.moego.svc.appointment.dto.AppointmentPetServiceDTO;
import com.moego.svc.appointment.dto.CancelAppointmentDTO;
import com.moego.svc.appointment.dto.CreateAppointmentResultDTO;
import com.moego.svc.appointment.helper.BusinessHelper;
import com.moego.svc.appointment.listener.event.CreateAppointmentEvent;
import com.moego.svc.appointment.service.AppointmentCompositeService;
import com.moego.svc.appointment.service.AppointmentServiceProxy;
import com.moego.svc.appointment.service.LodgingService;
import com.moego.svc.appointment.service.PetDetailServiceProxy;
import com.moego.svc.appointment.service.PricingRuleRecordApplyService;
import com.moego.svc.appointment.service.ServiceOperationService;
import com.moego.svc.appointment.service.remote.CompanyRemoteService;
import com.moego.svc.appointment.service.remote.LodgingRemoteService;
import com.moego.svc.appointment.service.remote.OfferingRemoteService;
import com.moego.svc.appointment.service.remote.OrderRemoteService;
import com.moego.svc.appointment.service.remote.OrganizationRemoteService;
import com.moego.svc.appointment.service.remote.PetRemoteService;
import com.moego.svc.appointment.utils.Pair;
import io.grpc.stub.StreamObserver;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

@ExtendWith(MockitoExtension.class)
class AppointmentControllerTest {

    @Mock
    private AppointmentServiceProxy appointmentService;

    @Mock
    private PetRemoteService petRemoteService;

    @Mock
    private AppointmentCompositeService appointmentCompositeService;

    @Mock
    private OfferingRemoteService offeringRemoteService;

    @Mock
    private OrderRemoteService orderRemoteService;

    @Mock
    private ApplicationEventPublisher publisher;

    @Mock
    private OrganizationRemoteService organizationRemoteService;

    @Mock
    private PricingRuleRecordApplyService pricingRuleApplyService;

    @Mock
    private ServiceOperationService serviceOperationService;

    @Mock
    private PetDetailServiceProxy petDetailService;

    @Mock
    private LodgingRemoteService lodgingRemoteService;

    @Mock
    private LodgingService lodgingService;

    @Mock
    private BusinessHelper businessHelper;

    @Mock
    private CompanyRemoteService companyRemoteService;

    @InjectMocks
    private AppointmentController appointmentController;

    @Test
    void testBatchQuickCheckIn() {
        // Define test data
        long companyId = 1L;
        long businessId = 1L;
        List<Long> petIds = Arrays.asList(1L, 2L, 3L, 4L);
        long serviceId = 1L;
        LocalDate now = LocalDate.now();

        // Mock the behavior of the appointmentService
        // pet 1L & appointment 1L -> skipCheckIn
        // pet 2L & appointment 2L -> directCheckIn
        // pet 3L & appointment 3L -> addServiceAndCheckIn
        // pet 4L -> createAppointmentAndCheckIn
        var petIdToAppointmentsMap = buildPetIdToAppointmentMap((int) serviceId);

        when(appointmentService.listAppointmentsForPet(any(), any(), any())).thenReturn(petIdToAppointmentsMap);

        // Mock the behavior of batchCreateAppointmentForQuickCheckIn
        when(petRemoteService.getPetCustomerMap(companyId, List.of(4L))).thenReturn(Map.of(4L, 4L));
        when(offeringRemoteService.listService(companyId, businessId, Map.of(4L, List.of(serviceId))))
                .thenReturn(Map.of(
                        4L,
                        Map.of(
                                serviceId,
                                CustomizedServiceView.newBuilder()
                                        .setMaxDuration(600)
                                        .build())));
        when(organizationRemoteService.getTimezone(companyId)).thenReturn("UTC");
        when(appointmentCompositeService.createAppointment(any(), anyList(), anyList()))
                .thenReturn(CreateAppointmentResultDTO.builder()
                        .newRecordInserted(true)
                        .appointmentId(4L)
                        .build());
        when(orderRemoteService.createOrder(any(), anyList(), anyList(), any(), eq(false)))
                .thenReturn(1L);
        doNothing().when(publisher).publishEvent(any());
        // Mock the behavior of batchCheckIn
        doNothing().when(appointmentService).batchCheckIn(Set.of(2L));
        // Mock the behavior of getLastLodgingUnitId
        when(appointmentCompositeService.getLastLodgingUnitId(anyLong(), anyLong(), anyList(), anyList(), anyList()))
                .thenReturn(Map.of());
        // Mock the behavior of batchAddServiceAndCheckIn
        AppointmentPetServiceDTO dto = new AppointmentPetServiceDTO()
                .setAppointmentId(3L)
                .setPetId(3L)
                .setServiceId(serviceId)
                .setCustomerId(2L)
                .setStartDate(now.toString());
        doNothing().when(appointmentCompositeService).batchAddServiceAndCheckIn(companyId, businessId, Set.of(dto));

        when(pricingRuleApplyService.applyPricingRule(anyLong(), anyLong(), anyLong()))
                .thenReturn(List.of());
        when(petRemoteService.getPetMap(anyLong(), anyList())).thenReturn(Map.of());
        when(petRemoteService.getPetSizeList(anyLong())).thenReturn(List.of());
        when(offeringRemoteService.getServiceModels(anyLong(), anyList()))
                .thenReturn(List.of(ServiceBriefView.getDefaultInstance()));
        when(lodgingRemoteService.getLodgingType(anyLong())).thenReturn(List.of());
        when(lodgingService.listLodgings(anyLong())).thenReturn(List.of());
        when(lodgingService.getAssignInfo(anyLong(), anyLong(), anyString(), anyString(), anyList()))
                .thenReturn(List.of());
        when(businessHelper.getBusinessWorkingHours(anyLong(), anyLong()))
                .thenReturn(WorkingHoursDef.getDefaultInstance());
        when(companyRemoteService.getTimezoneName(anyLong())).thenReturn("America/Los_Angeles");

        // Call the method under test
        BatchQuickCheckInRequest request = BatchQuickCheckInRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .addAllPetIds(petIds)
                .setServiceId(serviceId)
                .setDate(TimeConverter.INSTANCE.toGoogleDate(now))
                .setSource(AppointmentSource.WEB)
                .build();
        // Create response observer
        StreamObserver<BatchQuickCheckInResponse> responseObserver = mock(StreamObserver.class);
        appointmentController.batchQuickCheckIn(request, responseObserver);

        // Capture the response
        ArgumentCaptor<BatchQuickCheckInResponse> responseCaptor = forClass(BatchQuickCheckInResponse.class);
        verify(responseObserver).onNext(responseCaptor.capture());
        verify(responseObserver).onCompleted();

        // Verify the response data
        BatchQuickCheckInResponse response = responseCaptor.getValue();
        assertNotNull(response);
        assertEquals(response.getCreatedAppointmentIdsList(), List.of(4L));
        assertEquals(response.getUpdatedAppointmentIdsList(), List.of(2L, 3L));
    }

    private static Map<Long, List<Pair<MoeGroomingAppointment, List<MoeGroomingPetDetail>>>> buildPetIdToAppointmentMap(
            int serviceId) {
        Map<Long, List<Pair<MoeGroomingAppointment, List<MoeGroomingPetDetail>>>> petIdToAppointmentsMap =
                new HashMap<>();

        // skipCheckIn condition
        var skipCheckInAppointment = new MoeGroomingAppointment();
        skipCheckInAppointment.setId(1);
        skipCheckInAppointment.setStatus((byte) AppointmentStatus.CHECKED_IN_VALUE);
        skipCheckInAppointment.setCustomerId(2);
        var skipCheckInPetDetail = new MoeGroomingPetDetail();
        skipCheckInPetDetail.setServiceId(serviceId);
        petIdToAppointmentsMap.put(
                1L,
                Collections.singletonList(
                        Pair.of(skipCheckInAppointment, Collections.singletonList(skipCheckInPetDetail))));

        // directCheckIn condition
        var directCheckInAppointment = new MoeGroomingAppointment();
        directCheckInAppointment.setId(2);
        directCheckInAppointment.setStatus((byte) AppointmentStatus.UNCONFIRMED_VALUE);
        directCheckInAppointment.setCustomerId(2);
        var directCheckInPetDetail = new MoeGroomingPetDetail();
        directCheckInPetDetail.setServiceId(serviceId);
        petIdToAppointmentsMap.put(
                2L,
                Collections.singletonList(
                        Pair.of(directCheckInAppointment, Collections.singletonList(directCheckInPetDetail))));

        // addServiceAndCheckIn condition
        var addServiceAndCheckInAppointment = new MoeGroomingAppointment();
        addServiceAndCheckInAppointment.setId(3);
        addServiceAndCheckInAppointment.setStatus((byte) AppointmentStatus.UNCONFIRMED_VALUE);
        addServiceAndCheckInAppointment.setServiceTypeInclude(ServiceItemEnum.GROOMING.getBitValue());
        addServiceAndCheckInAppointment.setCustomerId(2);
        var addServiceAndCheckInPetDetail = new MoeGroomingPetDetail();
        addServiceAndCheckInPetDetail.setServiceId(0);
        petIdToAppointmentsMap.put(
                3L,
                Collections.singletonList(Pair.of(
                        addServiceAndCheckInAppointment, Collections.singletonList(addServiceAndCheckInPetDetail))));

        // createAppointmentAndCheckIn condition
        petIdToAppointmentsMap.put(4L, List.of());

        return petIdToAppointmentsMap;
    }

    @Test
    void testBatchBookAgainAppointment() {
        // 定义测试数据
        long companyId = 1L;
        Integer businessId = 1;
        List<Long> appointmentIds = Arrays.asList(1L, 2L, 3L);
        LocalDate targetDate = LocalDate.now().plusDays(7); // 假设重新预约在一周后
        int startTime = 540; // 9:00 AM
        int endTime = 600; // 10:00 AM

        // 模拟 appointmentService.getAppointments
        List<MoeGroomingAppointment> appointments = new ArrayList<>();
        for (Long id : appointmentIds) {
            MoeGroomingAppointment appointment = new MoeGroomingAppointment();
            appointment.setId(id.intValue());
            appointment.setCompanyId(companyId);
            appointment.setBusinessId(businessId);
            appointment.setAppointmentDate(LocalDate.now().toString());
            appointment.setAppointmentEndDate(LocalDate.now().toString());
            appointment.setAppointmentStartTime(startTime);
            appointment.setAppointmentEndTime(endTime);
            appointment.setCustomerId(id.intValue());
            appointments.add(appointment);
        }
        when(appointmentService.getAppointments(companyId, appointmentIds)).thenReturn(appointments);

        // 模拟 petDetailService.getPetDetailsByAppointmentIds
        Map<Long, List<MoeGroomingPetDetail>> petDetailsByAppointmentIds = new HashMap<>();
        for (Long id : appointmentIds) {
            MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
            petDetail.setId(id.intValue());
            petDetail.setPetId(id.intValue());
            petDetail.setServiceId(1);
            petDetail.setStartDate(LocalDate.now().toString());
            petDetail.setStartTime(540L); // 9:00 AM
            petDetail.setEndDate(LocalDate.now().toString());
            petDetail.setEndTime(600L); // 10:00 AM
            petDetailsByAppointmentIds.put(id, List.of(petDetail));
        }
        // 单独添加一个多petDetail的预约
        MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
        petDetail.setId(1);
        petDetail.setPetId(1);
        petDetail.setServiceId(1);
        petDetail.setStartDate(LocalDate.now().toString());
        petDetail.setStartTime(540L); // 9:00 AM
        petDetail.setEndDate(LocalDate.now().toString());
        petDetail.setEndTime(600L); // 10:00 AM
        petDetailsByAppointmentIds.put(3L, List.of(petDetail, petDetail));
        when(petDetailService.getPetDetailsByAppointmentIds(appointmentIds)).thenReturn(petDetailsByAppointmentIds);

        // 模拟 serviceOperationService.getServiceOperationMapByPetDetailIds
        Map<Long, List<MoeGroomingServiceOperation>> serviceOperationMap = new HashMap<>();
        for (Long id : appointmentIds) {
            MoeGroomingServiceOperation operation = new MoeGroomingServiceOperation();
            operation.setId(id);
            serviceOperationMap.put(id, Collections.singletonList(operation));
        }
        when(serviceOperationService.getServiceOperationMapByPetDetailIds(appointmentIds))
                .thenReturn(serviceOperationMap);

        // 修改模拟 appointmentCompositeService.createAppointment
        AtomicLong newAppointmentId = new AtomicLong(100); // 用于生成新的预约 ID
        when(appointmentCompositeService.createAppointment(any(), anyList(), anyList()))
                .thenAnswer(invocation -> {
                    MoeGroomingAppointment appointment = invocation.getArgument(0);
                    long appointmentId = newAppointmentId.getAndIncrement();
                    appointment.setId((int) appointmentId); // 设置新的ID
                    appointment.setAppointmentDate(targetDate.toString()); // 设置新的日期
                    appointment.setAppointmentEndDate(targetDate.toString()); // 设置新的结束日期
                    return CreateAppointmentResultDTO.builder()
                            .newRecordInserted(true)
                            .appointmentId(appointmentId)
                            .build();
                });

        // 模拟 offeringRemoteService.listService
        Map<Long, Map<Long, CustomizedServiceView>> petServiceMap = new HashMap<>();
        for (Long appointmentId : appointmentIds) {
            Map<Long, CustomizedServiceView> serviceMap = new HashMap<>();
            serviceMap.put(
                    1L,
                    CustomizedServiceView.newBuilder()
                            .setId(1L)
                            .setMaxDuration(60)
                            .setPrice(50.0)
                            .build());
            petServiceMap.put(appointmentId, serviceMap);
        }
        when(offeringRemoteService.listService(anyLong(), anyLong(), anyMap())).thenReturn(petServiceMap);

        // 模拟其他服务
        when(pricingRuleApplyService.applyPricingRule(anyLong(), anyLong(), anyLong()))
                .thenReturn(List.of());
        when(orderRemoteService.createOrder(any(), anyList(), anyList(), any(), eq(false)))
                .thenReturn(1L);
        doNothing().when(publisher).publishEvent(any());

        // 调用被测试的方法
        BatchBookAgainAppointmentRequest request = BatchBookAgainAppointmentRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllAppointmentIds(appointmentIds)
                .setTargetDate(TimeConverter.INSTANCE.toGoogleDate(targetDate))
                .setRebookBy(1L)
                .build();
        StreamObserver<BatchBookAgainAppointmentResponse> responseObserver = mock(StreamObserver.class);
        appointmentController.batchBookAgainAppointment(request, responseObserver);

        // 捕获响应
        ArgumentCaptor<BatchBookAgainAppointmentResponse> responseCaptor =
                ArgumentCaptor.forClass(BatchBookAgainAppointmentResponse.class);
        verify(responseObserver).onNext(responseCaptor.capture());
        verify(responseObserver).onCompleted();

        // 验证响应数据
        BatchBookAgainAppointmentResponse response = responseCaptor.getValue();
        assertNotNull(response);
        assertEquals(3, response.getAppointmentsCount());

        // 验证新创建的预约
        List<AppointmentModel> newAppointments = response.getAppointmentsList();
        for (int i = 0; i < newAppointments.size(); i++) {
            AppointmentModel newAppointment = newAppointments.get(i);
            assertEquals(100 + i, newAppointment.getId());
            assertEquals(targetDate.toString(), newAppointment.getAppointmentDate());
            assertEquals(targetDate.toString(), newAppointment.getAppointmentEndDate());
            assertEquals(startTime, newAppointment.getAppointmentStartTime());
            assertEquals(endTime, newAppointment.getAppointmentEndTime());
        }

        // 验证服务调用
        verify(appointmentService).getAppointments(companyId, appointmentIds);
        verify(petDetailService).getPetDetailsByAppointmentIds(appointmentIds);
        verify(serviceOperationService).getServiceOperationMapByPetDetailIds(appointmentIds);
        verify(offeringRemoteService, times(3)).listService(anyLong(), anyLong(), anyMap());
        verify(appointmentCompositeService, times(3)).createAppointment(any(), anyList(), anyList());
        verify(pricingRuleApplyService, times(3)).applyPricingRule(anyLong(), anyLong(), anyLong());
        verify(orderRemoteService, times(3)).createOrder(any(), anyList(), anyList(), any(), eq(false));
        verify(publisher, times(3)).publishEvent(any(CreateAppointmentEvent.class));
    }

    @Test
    void testBatchCancelAppointment() {
        // 定义测试数据
        long companyId = 1L;
        List<Long> appointmentIds = Arrays.asList(1L, 2L);
        long cancelBy = 100L;
        String cancelReason = "测试取消原因";

        List<MoeGroomingAppointment> appointments = new ArrayList<>();

        MoeGroomingAppointment appointment1 = new MoeGroomingAppointment();
        appointment1.setId(1);
        appointment1.setCompanyId(companyId);
        appointment1.setStatus(AppointmentStatusEnum.READY.getValue());
        appointments.add(appointment1);

        MoeGroomingAppointment appointment2 = new MoeGroomingAppointment();
        appointment2.setId(2);
        appointment2.setCompanyId(companyId);
        appointment2.setStatus(AppointmentStatusEnum.FINISHED.getValue()); // 设置为不可取消状态
        appointments.add(appointment2);

        when(appointmentService.getAppointments(companyId, appointmentIds)).thenReturn(appointments);

        // 模拟 appointmentCompositeService.cancelAppointment
        doNothing().when(appointmentCompositeService).cancelAppointment(any(CancelAppointmentDTO.class));

        // 测试场景1：提供取消原因
        BatchCancelAppointmentRequest requestWithReason = BatchCancelAppointmentRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllAppointmentIds(appointmentIds)
                .setCancelBy(cancelBy)
                .setCancelReason(cancelReason)
                .build();

        StreamObserver<BatchCancelAppointmentResponse> responseObserver1 = mock(StreamObserver.class);
        appointmentController.batchCancelAppointment(requestWithReason, responseObserver1);

        // 测试场景2：不提供取消原因
        BatchCancelAppointmentRequest requestWithoutReason = BatchCancelAppointmentRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllAppointmentIds(appointmentIds)
                .setCancelBy(cancelBy)
                .build();

        StreamObserver<BatchCancelAppointmentResponse> responseObserver2 = mock(StreamObserver.class);
        appointmentController.batchCancelAppointment(requestWithoutReason, responseObserver2);

        verify(appointmentService, times(2)).getAppointments(companyId, appointmentIds);

        // 验证 cancelAppointment 被调用了2次（每个场景1次，其中一个appt不可取消）
        verify(appointmentCompositeService, times(2)).cancelAppointment(argThat(dto -> {
            assertEquals(AppointmentUpdatedBy.BY_BUSINESS, dto.getCancelByType());
            assertEquals(AppointmentNoShowStatus.NOT_NO_SHOW, dto.getNoShow());
            assertEquals(cancelBy, dto.getCancelBy());
            return true;
        }));

        // 验证响应
        verify(responseObserver1).onNext(any(BatchCancelAppointmentResponse.class));
        verify(responseObserver1).onCompleted();
        verify(responseObserver2).onNext(any(BatchCancelAppointmentResponse.class));
        verify(responseObserver2).onCompleted();
    }

    @Test
    void testCalculateAppointmentEndTime_GreaterThanWorkingHour() {
        int startTime = 840; // 14:00
        int duration = 60 * 6; // 6 hour, serviceEndTime = 20:00
        // 9:00-12:00, 14:00-19:00
        List<TimeRangeDef> timeRanges = List.of(
                TimeRangeDef.newBuilder().setStartTime(540).setEndTime(720).build(),
                TimeRangeDef.newBuilder().setStartTime(840).setEndTime(1140).build());
        when(businessHelper.getBusinessWorkingHours(anyLong(), anyLong()))
                .thenReturn(WorkingHoursDef.newBuilder()
                        .addAllMonday(timeRanges)
                        .addAllTuesday(timeRanges)
                        .addAllWednesday(timeRanges)
                        .addAllThursday(timeRanges)
                        .addAllFriday(timeRanges)
                        .addAllSaturday(timeRanges)
                        .addAllSunday(timeRanges)
                        .build());
        when(companyRemoteService.getTimezoneName(anyLong())).thenReturn("GMT");

        int endTime = appointmentController.calculateAppointmentEndTime(1L, 1L, startTime, duration);
        int except = 1140; // 840 + 60 * 6 = 1200 > 1140.

        assertEquals(except, endTime);
    }

    @Test
    void testCalculateAppointmentEndTime_LessThanWorkingHour() {
        int startTime = 840; // 14:00
        int duration = 60 * 3; // 3 hour, serviceEndTime = 17:00
        // 9:00-12:00, 14:00-19:00
        List<TimeRangeDef> timeRanges = List.of(
                TimeRangeDef.newBuilder().setStartTime(540).setEndTime(720).build(),
                TimeRangeDef.newBuilder().setStartTime(840).setEndTime(1140).build());
        when(businessHelper.getBusinessWorkingHours(anyLong(), anyLong()))
                .thenReturn(WorkingHoursDef.newBuilder()
                        .addAllMonday(timeRanges)
                        .addAllTuesday(timeRanges)
                        .addAllWednesday(timeRanges)
                        .addAllThursday(timeRanges)
                        .addAllFriday(timeRanges)
                        .addAllSaturday(timeRanges)
                        .addAllSunday(timeRanges)
                        .build());
        when(companyRemoteService.getTimezoneName(anyLong())).thenReturn("GMT");

        int endTime = appointmentController.calculateAppointmentEndTime(1L, 1L, startTime, duration);
        int except = startTime + duration;

        assertEquals(except, endTime);
    }

    @Test
    void testCalculateAppointmentEndTime_NullWorkingHour() {
        int startTime = 840; // 14:00
        int duration = 60 * 6; // 6 hour, serviceEndTime = 20:00
        List<TimeRangeDef> timeRanges = List.of();
        when(businessHelper.getBusinessWorkingHours(anyLong(), anyLong()))
                .thenReturn(WorkingHoursDef.newBuilder()
                        .addAllMonday(timeRanges)
                        .addAllTuesday(timeRanges)
                        .addAllWednesday(timeRanges)
                        .addAllThursday(timeRanges)
                        .addAllFriday(timeRanges)
                        .addAllSaturday(timeRanges)
                        .addAllSunday(timeRanges)
                        .build());
        when(companyRemoteService.getTimezoneName(anyLong())).thenReturn("GMT");

        int endTime = appointmentController.calculateAppointmentEndTime(1L, 1L, startTime, duration);
        int except = startTime + duration;

        assertEquals(except, endTime);
    }

    @Test
    void testCalculateAppointmentEndTime_NullWorkingHourAndAcrossDay() {
        int startTime = 840; // 14:00
        int duration = 60 * 24; // 24 hour, serviceEndTime = 14:00
        List<TimeRangeDef> timeRanges = List.of();
        when(businessHelper.getBusinessWorkingHours(anyLong(), anyLong()))
                .thenReturn(WorkingHoursDef.newBuilder()
                        .addAllMonday(timeRanges)
                        .addAllTuesday(timeRanges)
                        .addAllWednesday(timeRanges)
                        .addAllThursday(timeRanges)
                        .addAllFriday(timeRanges)
                        .addAllSaturday(timeRanges)
                        .addAllSunday(timeRanges)
                        .build());
        when(companyRemoteService.getTimezoneName(anyLong())).thenReturn("GMT");

        int endTime = appointmentController.calculateAppointmentEndTime(1L, 1L, startTime, duration);
        int except = 1439;

        assertEquals(except, endTime);
    }

    @Test
    void testCalculateAppointmentEndTime_AfterWorkingHour() {
        int startTime = 1200; // 20:00
        int duration = 60 * 6; // 6 hour, serviceEndTime = 02:00
        // 9:00-12:00, 14:00-19:00
        List<TimeRangeDef> timeRanges = List.of(
                TimeRangeDef.newBuilder().setStartTime(540).setEndTime(720).build(),
                TimeRangeDef.newBuilder().setStartTime(840).setEndTime(1140).build());
        when(businessHelper.getBusinessWorkingHours(anyLong(), anyLong()))
                .thenReturn(WorkingHoursDef.newBuilder()
                        .addAllMonday(timeRanges)
                        .addAllTuesday(timeRanges)
                        .addAllWednesday(timeRanges)
                        .addAllThursday(timeRanges)
                        .addAllFriday(timeRanges)
                        .addAllSaturday(timeRanges)
                        .addAllSunday(timeRanges)
                        .build());
        when(companyRemoteService.getTimezoneName(anyLong())).thenReturn("GMT");

        int endTime = appointmentController.calculateAppointmentEndTime(1L, 1L, startTime, duration);
        int except = 1439;

        assertEquals(except, endTime);
    }

    @Test
    void testGetTimeOverlapAppointmentList_EmptyResult() {
        // 准备测试数据
        long companyId = 1L;
        List<Long> customerIdList = List.of(1L, 2L);
        List<Long> petIdsList = List.of(1L, 2L);
        LocalDate startDate = LocalDate.now();
        LocalDate endDate = startDate.plusDays(7);

        // 创建请求对象
        GetTimeOverlapAppointmentListRequest request = GetTimeOverlapAppointmentListRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllCustomerId(customerIdList)
                .addAllPetIds(petIdsList)
                .setFilter(GetTimeOverlapAppointmentListRequest.Filter.newBuilder()
                        .setDateRange(createDateRangeInterval(startDate, endDate))
                        .build())
                .build();

        // 模拟 appointmentService.getTimeOverlapAppointmentList 返回空结果
        when(appointmentService.getTimeOverlapAppointmentList(
                        eq(companyId), anyList(), anyList(), any(GetTimeOverlapAppointmentListRequest.Filter.class)))
                .thenReturn(Map.of());

        // 创建响应观察者
        StreamObserver<GetTimeOverlapAppointmentListResponse> responseObserver = mock(StreamObserver.class);

        // 调用被测试的方法
        appointmentController.getTimeOverlapAppointmentList(request, responseObserver);

        // 捕获响应
        ArgumentCaptor<GetTimeOverlapAppointmentListResponse> responseCaptor =
                ArgumentCaptor.forClass(GetTimeOverlapAppointmentListResponse.class);
        verify(responseObserver).onNext(responseCaptor.capture());
        verify(responseObserver).onCompleted();

        // 验证响应数据
        GetTimeOverlapAppointmentListResponse response = responseCaptor.getValue();
        assertNotNull(response);
        assertEquals(0, response.getAppointmentListMap().size());
    }

    @Test
    void testGetTimeOverlapAppointmentList_WithResults() {
        // 准备测试数据
        long companyId = 1L;
        List<Long> customerIdList = List.of(1L, 2L);
        List<Long> petIdsList = List.of(1L, 2L);
        LocalDate startDate = LocalDate.now();
        LocalDate endDate = startDate.plusDays(7);

        // 创建请求对象
        GetTimeOverlapAppointmentListRequest request = GetTimeOverlapAppointmentListRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllCustomerId(customerIdList)
                .addAllPetIds(petIdsList)
                .setFilter(GetTimeOverlapAppointmentListRequest.Filter.newBuilder()
                        .setDateRange(createDateRangeInterval(startDate, endDate))
                        .build())
                .build();

        // 创建模拟的预约数据
        Map<Long, List<AppointmentModel>> petIdToAppointmentList = new HashMap<>();

        // 为宠物ID 1 创建预约列表
        List<AppointmentModel> pet1Appointments = List.of(
                AppointmentModel.newBuilder()
                        .setId(1L)
                        .setCustomerId(1L)
                        .setAppointmentDate(startDate.toString())
                        .setAppointmentStartTime(540) // 9:00 AM
                        .setAppointmentEndTime(600) // 10:00 AM
                        .setStatus(AppointmentStatus.UNCONFIRMED)
                        .build(),
                AppointmentModel.newBuilder()
                        .setId(2L)
                        .setCustomerId(1L)
                        .setAppointmentDate(startDate.plusDays(1).toString())
                        .setAppointmentStartTime(660) // 11:00 AM
                        .setAppointmentEndTime(720) // 12:00 PM
                        .setStatus(AppointmentStatus.UNCONFIRMED)
                        .build());
        petIdToAppointmentList.put(1L, pet1Appointments);

        // 为宠物ID 2 创建预约列表
        List<AppointmentModel> pet2Appointments = List.of(AppointmentModel.newBuilder()
                .setId(3L)
                .setCustomerId(2L)
                .setAppointmentDate(startDate.toString())
                .setAppointmentStartTime(780) // 13:00 PM
                .setAppointmentEndTime(840) // 14:00 PM
                .setStatus(AppointmentStatus.UNCONFIRMED)
                .build());
        petIdToAppointmentList.put(2L, pet2Appointments);

        // 模拟 appointmentService.getTimeOverlapAppointmentList 返回预约数据
        when(appointmentService.getTimeOverlapAppointmentList(
                        eq(companyId), anyList(), anyList(), any(GetTimeOverlapAppointmentListRequest.Filter.class)))
                .thenReturn(petIdToAppointmentList);

        // 创建响应观察者
        StreamObserver<GetTimeOverlapAppointmentListResponse> responseObserver = mock(StreamObserver.class);

        // 调用被测试的方法
        appointmentController.getTimeOverlapAppointmentList(request, responseObserver);

        // 捕获响应
        ArgumentCaptor<GetTimeOverlapAppointmentListResponse> responseCaptor =
                ArgumentCaptor.forClass(GetTimeOverlapAppointmentListResponse.class);
        verify(responseObserver).onNext(responseCaptor.capture());
        verify(responseObserver).onCompleted();

        // 验证响应数据
        GetTimeOverlapAppointmentListResponse response = responseCaptor.getValue();
        assertNotNull(response);
        assertEquals(2, response.getAppointmentListMap().size());

        // 验证宠物ID 1 的预约列表
        assertTrue(response.getAppointmentListMap().containsKey(1L));
        AppointmentList pet1AppointmentList = response.getAppointmentListMap().get(1L);
        assertEquals(2, pet1AppointmentList.getAppointmentsCount());
        assertEquals(1L, pet1AppointmentList.getAppointments(0).getId());
        assertEquals(2L, pet1AppointmentList.getAppointments(1).getId());

        // 验证宠物ID 2 的预约列表
        assertTrue(response.getAppointmentListMap().containsKey(2L));
        AppointmentList pet2AppointmentList = response.getAppointmentListMap().get(2L);
        assertEquals(1, pet2AppointmentList.getAppointmentsCount());
        assertEquals(3L, pet2AppointmentList.getAppointments(0).getId());
    }

    private Interval createDateRangeInterval(LocalDate startDate, LocalDate endDate) {
        LocalDateTime startDateTime = startDate.atStartOfDay();
        LocalDateTime endDateTime = endDate.atTime(23, 59, 59);

        Timestamp startTimestamp = Timestamp.newBuilder()
                .setSeconds(startDateTime.toEpochSecond(ZoneOffset.UTC))
                .build();

        Timestamp endTimestamp = Timestamp.newBuilder()
                .setSeconds(endDateTime.toEpochSecond(ZoneOffset.UTC))
                .build();

        return Interval.newBuilder()
                .setStartTime(startTimestamp)
                .setEndTime(endTimestamp)
                .build();
    }

    // Helper methods for creating test data
    private MoeGroomingPetDetail createMoeGroomingPetDetail(
            Long id,
            BigDecimal servicePrice,
            Integer quantity,
            BigDecimal totalPrice,
            Long lodgingId,
            String startDate,
            String endDate,
            int dateType,
            Long serviceId,
            Integer serviceItemType,
            int serviceType,
            int priceUnit) {
        MoeGroomingPetDetail detail = new MoeGroomingPetDetail();
        detail.setId(id.intValue());
        detail.setServicePrice(servicePrice);
        detail.setQuantity(quantity);
        detail.setTotalPrice(totalPrice);
        detail.setLodgingId(lodgingId);
        detail.setStartDate(startDate);
        detail.setEndDate(endDate);
        detail.setDateType(dateType);
        detail.setServiceId(serviceId.intValue());
        detail.setServiceItemType(serviceItemType.byteValue());
        detail.setServiceType(serviceType);
        detail.setPriceUnit(priceUnit);
        return detail;
    }

    private CustomizedServiceView createCustomizedServiceView(String name, String description) {
        return CustomizedServiceView.newBuilder()
                .setName(name)
                .setDescription(description)
                .build();
    }

    private StaffModel createStaffModel(String firstName) {
        return StaffModel.newBuilder().setId(2000L).setFirstName(firstName).build();
    }

    private TaxRuleModel createTaxRuleModel() {
        return TaxRuleModel.newBuilder()
                .setId(1L)
                .setName("Standard Tax")
                .setRate(0.08f)
                .build();
    }

    private Map<Long, LodgingUnitModel> createLodgingUnitMap(Long lodgingId, Long lodgingTypeId, String name) {
        Map<Long, LodgingUnitModel> map = new HashMap<>();
        LodgingUnitModel lodging = LodgingUnitModel.newBuilder()
                .setLodgingTypeId(lodgingTypeId)
                .setName(name)
                .build();
        map.put(lodgingId, lodging);
        return map;
    }

    private Map<Long, LodgingTypeModel> createLodgingTypeMap(Long lodgingTypeId, String name) {
        Map<Long, LodgingTypeModel> map = new HashMap<>();
        LodgingTypeModel lodgingType =
                LodgingTypeModel.newBuilder().setName(name).build();
        map.put(lodgingTypeId, lodgingType);
        return map;
    }

    private List<BoardingSplitLodging> createSplitLodgings() {
        return List.of(
                createBoardingSplitLodging(
                        LocalDateTime.of(2025, 5, 20, 0, 0),
                        LocalDateTime.of(2025, 5, 22, 0, 0),
                        BigDecimal.valueOf(80.00)),
                createBoardingSplitLodging(
                        LocalDateTime.of(2025, 5, 22, 0, 0),
                        LocalDateTime.of(2025, 5, 23, 0, 0),
                        BigDecimal.valueOf(120.00)));
    }

    private BoardingSplitLodging createBoardingSplitLodging(
            LocalDateTime startDateTime, LocalDateTime endDateTime, BigDecimal price) {
        BoardingSplitLodging splitLodging = new BoardingSplitLodging();
        splitLodging.setStartDateTime(startDateTime);
        splitLodging.setEndDateTime(endDateTime);
        splitLodging.setPrice(price);
        return splitLodging;
    }

    private List<PricingRuleRecordApplyLog> createPricingRuleApplyLogs() {
        PricingRule multiplePetRule =
                PricingRule.newBuilder().setType(RuleType.MULTIPLE_PET).build();
        return List.of(
                createPricingRuleRecordApplyLog(
                        "2025-05-20", BigDecimal.valueOf(100.00), BigDecimal.valueOf(90.00), true, multiplePetRule),
                createPricingRuleRecordApplyLog(
                        "2025-05-21", BigDecimal.valueOf(100.00), BigDecimal.valueOf(90.00), true, multiplePetRule));
    }

    private List<PricingRuleRecordApplyLog> createExtendedPricingRuleApplyLogs() {
        PricingRule multiplePetRule =
                PricingRule.newBuilder().setType(RuleType.MULTIPLE_PET).build();
        return List.of(
                createPricingRuleRecordApplyLog(
                        "2025-05-20", BigDecimal.valueOf(80.00), BigDecimal.valueOf(70.00), true, multiplePetRule),
                createPricingRuleRecordApplyLog(
                        "2025-05-21", BigDecimal.valueOf(80.00), BigDecimal.valueOf(70.00), true, multiplePetRule),
                createPricingRuleRecordApplyLog(
                        "2025-05-22", BigDecimal.valueOf(120.00), BigDecimal.valueOf(110.00), true, multiplePetRule));
    }

    private List<PricingRuleRecordApplyLog> createFullDiscountPricingRuleApplyLogs() {
        PricingRule promotionRule =
                PricingRule.newBuilder().setType(RuleType.PEAK_DATE).build();
        return List.of(createPricingRuleRecordApplyLog(
                "2025-05-20", BigDecimal.valueOf(100.00), BigDecimal.ZERO, true, promotionRule));
    }

    private PricingRuleRecordApplyLog createPricingRuleRecordApplyLog(
            String serviceDate,
            BigDecimal originalPrice,
            BigDecimal adjustedPrice,
            Boolean isUsingRule,
            PricingRule pricingRule) {
        PricingRuleRecordApplyLog log = new PricingRuleRecordApplyLog();
        log.setServiceDate(serviceDate);
        log.setOriginalPrice(originalPrice);
        log.setAdjustedPrice(adjustedPrice);
        log.setIsUsingRule(isUsingRule);
        log.setPricingRule(pricingRule);
        return log;
    }

    private BigDecimal calculateLineItemsSum(List<LineItem> lineItems) {
        return lineItems.stream()
                .map(lineItem -> MoneyUtils.fromGoogleMoney(lineItem.getLineTotal()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Nested
    @DisplayName("convertPetDetailToServiceItem - Grooming Service Tests")
    class GroomingServiceTest {

        @Test
        @DisplayName("should have total price equal to unit price for simple grooming service")
        void shouldHaveTotalPriceEqualToUnitPriceForSimpleGroomingService() {
            // Arrange - Grooming: 1 day, quantity=1, totalPrice=unitPrice
            BigDecimal servicePrice = BigDecimal.valueOf(85.00);
            Integer quantity = 1;
            BigDecimal totalPrice = servicePrice; // Same as unit price for grooming

            MoeGroomingPetDetail petDetail = createGroomingPetDetail(
                    1L, servicePrice, quantity, totalPrice, "2025-05-20", "2025-05-20", 1L, 11111L);
            CustomizedServiceView service = createCustomizedServiceView("Premium Grooming", "Full service grooming");
            StaffModel staff = createStaffModel("Alice");
            TaxRuleModel tax = createTaxRuleModel();

            // Act
            ServiceItem result = AppointmentController.convertPetDetailToServiceItem(
                    petDetail,
                    service,
                    staff,
                    tax,
                    new HashMap<>(),
                    new HashMap<>(),
                    new ArrayList<>(),
                    "UTC",
                    new ArrayList<>());

            // Assert
            assertThat(result.getTotalPrice()).isEqualTo(MoneyUtils.toGoogleMoney(totalPrice));

            BigDecimal lineItemsSum = calculateLineItemsSum(result.getLineItemsList());
            assertThat(lineItemsSum).isEqualByComparingTo(totalPrice);
            assertThat(MoneyUtils.toGoogleMoney(lineItemsSum)).isEqualTo(result.getTotalPrice());

            // Verify grooming service structure - simple single line item
            assertThat(result.getLineItemsList()).hasSize(1);
            LineItem lineItem = result.getLineItemsList().get(0);
            assertThat(lineItem.getItemName()).isEqualTo("Service - Alice");
            assertThat(lineItem.getUnitPrice()).isEqualTo(MoneyUtils.toGoogleMoney(servicePrice));
            assertThat(lineItem.getQuantity()).isEqualTo(1);
            assertThat(lineItem.getLineTotal()).isEqualTo(MoneyUtils.toGoogleMoney(totalPrice));
            assertThat(lineItem.getItemType()).isEqualTo(LineItem.LineItemType.SERVICE_PRICE);
        }

        @Test
        @DisplayName("should have total price equal to unit price for nail trimming")
        void shouldHaveTotalPriceEqualToUnitPriceForNailTrimming() {
            // Arrange - Simple grooming service
            BigDecimal servicePrice = BigDecimal.valueOf(25.00);
            Integer quantity = 1;
            BigDecimal totalPrice = servicePrice;

            MoeGroomingPetDetail petDetail = createGroomingPetDetail(
                    2L, servicePrice, quantity, totalPrice, "2025-05-21", "2025-05-21", 2L, 22222L);
            CustomizedServiceView service = createCustomizedServiceView("Nail Trimming", "Professional nail care");
            StaffModel staff = createStaffModel("Bob");
            TaxRuleModel tax = createTaxRuleModel();

            // Act
            ServiceItem result = AppointmentController.convertPetDetailToServiceItem(
                    petDetail,
                    service,
                    staff,
                    tax,
                    new HashMap<>(),
                    new HashMap<>(),
                    new ArrayList<>(),
                    "UTC",
                    new ArrayList<>());

            // Assert
            assertThat(result.getTotalPrice()).isEqualTo(MoneyUtils.toGoogleMoney(totalPrice));

            BigDecimal lineItemsSum = calculateLineItemsSum(result.getLineItemsList());
            assertThat(lineItemsSum).isEqualByComparingTo(totalPrice);
            assertThat(MoneyUtils.toGoogleMoney(lineItemsSum)).isEqualTo(result.getTotalPrice());

            // Verify simple structure
            assertThat(result.getLineItemsList()).hasSize(1);
            assertThat(result.getServiceType()).isEqualTo(ServiceType.SERVICE);
            assertThat(result.getServiceItemType()).isEqualTo(ServiceItemType.GROOMING);
        }

        // Helper methods for creating test data
        private MoeGroomingPetDetail createGroomingPetDetail(
                Long id,
                BigDecimal servicePrice,
                Integer quantity,
                BigDecimal totalPrice,
                String startDate,
                String endDate,
                Long serviceId,
                Long staffId) {
            MoeGroomingPetDetail detail = new MoeGroomingPetDetail();
            detail.setId(id.intValue());
            detail.setServicePrice(servicePrice);
            detail.setQuantity(quantity);
            detail.setTotalPrice(totalPrice);
            detail.setLodgingId(0L); // Grooming has no lodging
            detail.setStartDate(startDate);
            detail.setEndDate(endDate);
            detail.setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE);
            detail.setServiceId(serviceId.intValue());
            detail.setServiceItemType((byte) ServiceItemType.GROOMING_VALUE);
            detail.setServiceType(ServiceType.SERVICE_VALUE);
            detail.setPriceUnit(ServicePriceUnit.PER_DAY_VALUE);
            detail.setStaffId(staffId.intValue());
            detail.setAssociatedServiceId(0L);
            return detail;
        }
    }

    @Nested
    @DisplayName("convertPetDetailToServiceItem - Boarding Service Tests")
    class BoardingServiceTest {

        @Test
        @DisplayName("should have total price equal to line items sum for boarding with lodging")
        void shouldHaveTotalPriceEqualToLineItemsSumForBoardingWithLodging() {
            // Arrange - Boarding: multi-day, with lodging
            Long lodgingId = 123L;
            Long lodgingTypeId = 456L;
            BigDecimal servicePrice = BigDecimal.valueOf(120.00);
            Integer quantity = 3; // 3 nights
            BigDecimal totalPrice = BigDecimal.valueOf(360.00); // 120 * 3

            MoeGroomingPetDetail petDetail = createBoardingPetDetail(
                    3L, servicePrice, quantity, totalPrice, lodgingId, "2025-05-20", "2025-05-23", 3L);
            CustomizedServiceView service = createCustomizedServiceView("Premium Boarding", "Luxury overnight care");
            Map<Long, LodgingUnitModel> idToLodging = createLodgingUnitMap(lodgingId, lodgingTypeId, "Deluxe Suite");
            Map<Long, LodgingTypeModel> idToLodgingType = createLodgingTypeMap(lodgingTypeId, "Premium");
            TaxRuleModel tax = createTaxRuleModel();

            // Act
            ServiceItem result = AppointmentController.convertPetDetailToServiceItem(
                    petDetail,
                    service,
                    null,
                    tax,
                    idToLodging,
                    idToLodgingType,
                    new ArrayList<>(),
                    "UTC",
                    new ArrayList<>());

            // Assert
            assertThat(result.getTotalPrice()).isEqualTo(MoneyUtils.toGoogleMoney(totalPrice));

            BigDecimal lineItemsSum = calculateLineItemsSum(result.getLineItemsList());
            assertThat(lineItemsSum).isEqualByComparingTo(totalPrice);
            assertThat(MoneyUtils.toGoogleMoney(lineItemsSum)).isEqualTo(result.getTotalPrice());

            // Verify boarding service structure with lodging
            assertThat(result.getLineItemsList()).hasSize(1);
            LineItem lineItem = result.getLineItemsList().get(0);
            assertThat(lineItem.getItemName()).isEqualTo("Service - Deluxe Suite(Premium)");
            assertThat(lineItem.getQuantity()).isEqualTo(3);
            assertThat(result.getServiceType()).isEqualTo(ServiceType.SERVICE);
            assertThat(result.getServiceItemType()).isEqualTo(ServiceItemType.BOARDING);
        }

        @Test
        @DisplayName("should have total price equal to line items sum for boarding with split lodgings")
        void shouldHaveTotalPriceEqualToLineItemsSumForBoardingWithSplitLodgings() {
            // Arrange - Boarding: with split lodgings (different rates for different periods)
            BigDecimal servicePrice = BigDecimal.valueOf(100.00);
            Integer quantity = 4; // 4 nights total
            BigDecimal totalPrice = BigDecimal.valueOf(380.00); // 2*90 + 2*100 = 180 + 200

            MoeGroomingPetDetail petDetail = createBoardingPetDetail(
                    4L, servicePrice, quantity, totalPrice, null, "2025-05-20", "2025-05-24", 4L);
            CustomizedServiceView service = createCustomizedServiceView("Standard Boarding", "Variable rate boarding");
            List<BoardingSplitLodging> splitLodgings = createBoardingSplitLodgings();
            TaxRuleModel tax = createTaxRuleModel();

            // Act
            ServiceItem result = AppointmentController.convertPetDetailToServiceItem(
                    petDetail,
                    service,
                    null,
                    tax,
                    new HashMap<>(),
                    new HashMap<>(),
                    splitLodgings,
                    "UTC",
                    new ArrayList<>());

            // Assert
            assertThat(result.getTotalPrice()).isEqualTo(MoneyUtils.toGoogleMoney(totalPrice));

            BigDecimal lineItemsSum = calculateLineItemsSum(result.getLineItemsList());
            assertThat(lineItemsSum).isEqualByComparingTo(totalPrice);
            assertThat(MoneyUtils.toGoogleMoney(lineItemsSum)).isEqualTo(result.getTotalPrice());

            // Verify split lodging structure
            assertThat(result.getLineItemsList()).hasSize(2);
            LineItem firstPeriod = result.getLineItemsList().get(0);
            assertThat(firstPeriod.getUnitPrice()).isEqualTo(MoneyUtils.toGoogleMoney(BigDecimal.valueOf(90.00)));
            assertThat(firstPeriod.getQuantity()).isEqualTo(2);
            assertThat(firstPeriod.getLineTotal()).isEqualTo(MoneyUtils.toGoogleMoney(BigDecimal.valueOf(180.00)));

            LineItem secondPeriod = result.getLineItemsList().get(1);
            assertThat(secondPeriod.getUnitPrice()).isEqualTo(MoneyUtils.toGoogleMoney(BigDecimal.valueOf(100.00)));
            assertThat(secondPeriod.getQuantity()).isEqualTo(2);
            assertThat(secondPeriod.getLineTotal()).isEqualTo(MoneyUtils.toGoogleMoney(BigDecimal.valueOf(200.00)));
        }

        @Test
        @DisplayName("should have total price equal to line items sum for boarding with multi-pet discount")
        void shouldHaveTotalPriceEqualToLineItemsSumForBoardingWithMultiPetDiscount() {
            // Arrange - Boarding: multi-pet scenario with discount
            BigDecimal servicePrice = BigDecimal.valueOf(100.00);
            Integer quantity = 3; // 3 nights
            BigDecimal originalTotal = BigDecimal.valueOf(300.00); // 100 * 3
            BigDecimal discountPerNight = BigDecimal.valueOf(-15.00); // Multi-pet discount
            BigDecimal totalDiscount = discountPerNight.multiply(BigDecimal.valueOf(quantity));
            BigDecimal finalTotalPrice = originalTotal.add(totalDiscount); // 300 - 45 = 255

            MoeGroomingPetDetail petDetail = createBoardingPetDetail(
                    5L, servicePrice, quantity, finalTotalPrice, null, "2025-05-20", "2025-05-23", 5L);
            CustomizedServiceView service =
                    createCustomizedServiceView("Multi-Pet Boarding", "Boarding with multi-pet discount");
            List<PricingRuleRecordApplyLog> pricingRuleApplyLogs = createMultiPetBoardingPricingRules();
            TaxRuleModel tax = createTaxRuleModel();

            // Act
            ServiceItem result = AppointmentController.convertPetDetailToServiceItem(
                    petDetail,
                    service,
                    null,
                    tax,
                    new HashMap<>(),
                    new HashMap<>(),
                    new ArrayList<>(),
                    "UTC",
                    pricingRuleApplyLogs);

            // Assert
            assertThat(result.getTotalPrice()).isEqualTo(MoneyUtils.toGoogleMoney(finalTotalPrice));

            BigDecimal lineItemsSum = calculateLineItemsSum(result.getLineItemsList());
            assertThat(lineItemsSum).isEqualByComparingTo(finalTotalPrice);
            assertThat(MoneyUtils.toGoogleMoney(lineItemsSum)).isEqualTo(result.getTotalPrice());

            // Verify structure: service item + pricing rule adjustment
            assertThat(result.getLineItemsList()).hasSize(2);

            LineItem serviceItem = result.getLineItemsList().stream()
                    .filter(item -> item.getItemType() == LineItem.LineItemType.SERVICE_PRICE)
                    .findFirst()
                    .orElseThrow();
            assertThat(serviceItem.getLineTotal()).isEqualTo(MoneyUtils.toGoogleMoney(originalTotal));

            LineItem discountItem = result.getLineItemsList().stream()
                    .filter(item -> item.getItemType() == LineItem.LineItemType.PRICING_RULE_ADJUSTMENT)
                    .findFirst()
                    .orElseThrow();
            assertThat(discountItem.getItemName()).isEqualTo("Multiple pets");
            assertThat(discountItem.getLineTotal()).isEqualTo(MoneyUtils.toGoogleMoney(totalDiscount));
        }

        @Test
        @DisplayName("should have total price equal to line items sum for boarding with peak dates surcharge")
        void shouldHaveTotalPriceEqualToLineItemsSumForBoardingWithPeakDatesSurcharge() {
            // Arrange - Boarding: peak dates scenario with surcharge
            BigDecimal servicePrice = BigDecimal.valueOf(100.00);
            Integer quantity = 2; // 2 nights during peak dates
            BigDecimal originalTotal = BigDecimal.valueOf(200.00); // 100 * 2
            BigDecimal surchargePerNight = BigDecimal.valueOf(20.00); // Peak dates surcharge
            BigDecimal totalSurcharge = surchargePerNight.multiply(BigDecimal.valueOf(quantity));
            BigDecimal finalTotalPrice = originalTotal.add(totalSurcharge); // 200 + 40 = 240

            MoeGroomingPetDetail petDetail = createBoardingPetDetail(
                    6L, servicePrice, quantity, finalTotalPrice, null, "2025-12-24", "2025-12-26", 6L);
            CustomizedServiceView service =
                    createCustomizedServiceView("Holiday Boarding", "Boarding during peak dates");
            List<PricingRuleRecordApplyLog> pricingRuleApplyLogs = createPeakDatesBoardingPricingRules();
            TaxRuleModel tax = createTaxRuleModel();

            // Act
            ServiceItem result = AppointmentController.convertPetDetailToServiceItem(
                    petDetail,
                    service,
                    null,
                    tax,
                    new HashMap<>(),
                    new HashMap<>(),
                    new ArrayList<>(),
                    "UTC",
                    pricingRuleApplyLogs);

            // Assert
            assertThat(result.getTotalPrice()).isEqualTo(MoneyUtils.toGoogleMoney(finalTotalPrice));

            BigDecimal lineItemsSum = calculateLineItemsSum(result.getLineItemsList());
            assertThat(lineItemsSum).isEqualByComparingTo(finalTotalPrice);
            assertThat(MoneyUtils.toGoogleMoney(lineItemsSum)).isEqualTo(result.getTotalPrice());

            // Verify structure: service item + peak dates surcharge
            assertThat(result.getLineItemsList()).hasSize(2);

            LineItem surchargeItem = result.getLineItemsList().stream()
                    .filter(item -> item.getItemType() == LineItem.LineItemType.PRICING_RULE_ADJUSTMENT)
                    .findFirst()
                    .orElseThrow();
            assertThat(surchargeItem.getItemName()).isEqualTo("Peak dates");
            assertThat(surchargeItem.getLineTotal()).isEqualTo(MoneyUtils.toGoogleMoney(totalSurcharge));
        }

        @Test
        @DisplayName(
                "should have total price equal to line items sum for complex boarding with split lodgings and multiple pricing rules")
        void shouldHaveTotalPriceEqualToLineItemsSumForComplexBoarding() {
            // Arrange - Boarding: complex scenario with split lodgings + multi-pet + peak dates
            BigDecimal servicePrice = BigDecimal.valueOf(100.00);
            Integer quantity = 4; // 4 nights
            BigDecimal splitLodgingTotal = BigDecimal.valueOf(380.00); // 2*90 + 2*100 from split lodgings
            BigDecimal discountAndSurcharge =
                    BigDecimal.valueOf(-60.00); // -15 * 4 nights, multi-pet discount and peak dates surcharge
            BigDecimal finalTotalPrice = splitLodgingTotal.add(discountAndSurcharge); // 380 - 60 + 40 = 360

            MoeGroomingPetDetail petDetail = createBoardingPetDetail(
                    7L, servicePrice, quantity, finalTotalPrice, null, "2025-07-04", "2025-07-08", 7L);
            CustomizedServiceView service =
                    createCustomizedServiceView("Complex Boarding", "Multi-rule boarding scenario");
            List<BoardingSplitLodging> splitLodgings = createBoardingSplitLodgings();
            List<PricingRuleRecordApplyLog> pricingRuleApplyLogs = createComplexBoardingPricingRules();
            TaxRuleModel tax = createTaxRuleModel();

            // Act
            ServiceItem result = AppointmentController.convertPetDetailToServiceItem(
                    petDetail,
                    service,
                    null,
                    tax,
                    new HashMap<>(),
                    new HashMap<>(),
                    splitLodgings,
                    "UTC",
                    pricingRuleApplyLogs);

            // Assert
            assertThat(result.getTotalPrice()).isEqualTo(MoneyUtils.toGoogleMoney(finalTotalPrice));

            BigDecimal lineItemsSum = calculateLineItemsSum(result.getLineItemsList());
            assertThat(lineItemsSum).isEqualByComparingTo(finalTotalPrice);
            assertThat(MoneyUtils.toGoogleMoney(lineItemsSum)).isEqualTo(result.getTotalPrice());

            // Verify complex structure: split lodging items (2) + pricing rule adjustments (2)
            List<LineItem> serviceItems = result.getLineItemsList().stream()
                    .filter(item -> item.getItemType() == LineItem.LineItemType.SERVICE_PRICE)
                    .toList();
            assertThat(serviceItems).hasSize(2); // From split lodgings

            List<LineItem> pricingRuleItems = result.getLineItemsList().stream()
                    .filter(item -> item.getItemType() == LineItem.LineItemType.PRICING_RULE_ADJUSTMENT)
                    .toList();
            assertThat(pricingRuleItems).hasSize(1); // Multi-pet discount + peak dates surcharge
            assertThat(pricingRuleItems.get(0).getLineTotal())
                    .isEqualTo(MoneyUtils.toGoogleMoney(discountAndSurcharge));
        }
    }

    // Helper methods for creating test data
    private MoeGroomingPetDetail createBoardingPetDetail(
            Long id,
            BigDecimal servicePrice,
            Integer quantity,
            BigDecimal totalPrice,
            Long lodgingId,
            String startDate,
            String endDate,
            Long serviceId) {
        MoeGroomingPetDetail detail = new MoeGroomingPetDetail();
        detail.setId(id.intValue());
        detail.setServicePrice(servicePrice);
        detail.setQuantity(quantity);
        detail.setTotalPrice(totalPrice);
        detail.setLodgingId(lodgingId);
        detail.setStartDate(startDate);
        detail.setEndDate(endDate);
        detail.setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE);
        detail.setServiceId(serviceId.intValue());
        detail.setServiceItemType((byte) ServiceItemType.BOARDING_VALUE);
        detail.setServiceType(ServiceType.SERVICE_VALUE);
        detail.setPriceUnit(ServicePriceUnit.PER_DAY_VALUE);
        detail.setStaffId(0); // Boarding typically doesn't have specific staff
        detail.setAssociatedServiceId(0L);
        return detail;
    }

    private List<BoardingSplitLodging> createBoardingSplitLodgings() {
        return List.of(
                createBoardingSplitLodging(
                        LocalDateTime.of(2025, 5, 20, 0, 0),
                        LocalDateTime.of(2025, 5, 22, 0, 0),
                        BigDecimal.valueOf(90.00)), // 2 nights at $90/night
                createBoardingSplitLodging(
                        LocalDateTime.of(2025, 5, 22, 0, 0),
                        LocalDateTime.of(2025, 5, 24, 0, 0),
                        BigDecimal.valueOf(100.00))); // 2 nights at $100/night
    }

    private List<PricingRuleRecordApplyLog> createMultiPetBoardingPricingRules() {
        PricingRule multiplePetRule =
                PricingRule.newBuilder().setType(RuleType.MULTIPLE_PET).build();
        return List.of(
                createPricingRuleRecordApplyLog(
                        "2025-05-20", BigDecimal.valueOf(100.00), BigDecimal.valueOf(85.00), true, multiplePetRule),
                createPricingRuleRecordApplyLog(
                        "2025-05-21", BigDecimal.valueOf(100.00), BigDecimal.valueOf(85.00), true, multiplePetRule),
                createPricingRuleRecordApplyLog(
                        "2025-05-22", BigDecimal.valueOf(100.00), BigDecimal.valueOf(85.00), true, multiplePetRule));
    }

    private List<PricingRuleRecordApplyLog> createPeakDatesBoardingPricingRules() {
        PricingRule peakDatesRule =
                PricingRule.newBuilder().setType(RuleType.PEAK_DATE).build();
        return List.of(
                createPricingRuleRecordApplyLog(
                        "2025-12-24", BigDecimal.valueOf(100.00), BigDecimal.valueOf(120.00), true, peakDatesRule),
                createPricingRuleRecordApplyLog(
                        "2025-12-25", BigDecimal.valueOf(100.00), BigDecimal.valueOf(120.00), true, peakDatesRule));
    }

    private List<PricingRuleRecordApplyLog> createComplexBoardingPricingRules() {
        PricingRule multiplePetRule =
                PricingRule.newBuilder().setType(RuleType.MULTIPLE_PET).build();
        PricingRule peakDatesRule =
                PricingRule.newBuilder().setType(RuleType.PEAK_DATE).build();
        return List.of(
                // Multi-pet discount: -$15 per night
                createPricingRuleRecordApplyLog(
                        "2025-07-04", BigDecimal.valueOf(90.00), BigDecimal.valueOf(75.00), true, multiplePetRule),
                createPricingRuleRecordApplyLog(
                        "2025-07-05", BigDecimal.valueOf(90.00), BigDecimal.valueOf(75.00), true, multiplePetRule),
                createPricingRuleRecordApplyLog(
                        "2025-07-06", BigDecimal.valueOf(100.00), BigDecimal.valueOf(85.00), true, multiplePetRule),
                createPricingRuleRecordApplyLog(
                        "2025-07-07", BigDecimal.valueOf(100.00), BigDecimal.valueOf(85.00), true, multiplePetRule),
                // Peak dates surcharge: +$10 per night
                createPricingRuleRecordApplyLog(
                        "2025-07-04", BigDecimal.valueOf(90.00), BigDecimal.valueOf(75.00), true, peakDatesRule),
                createPricingRuleRecordApplyLog(
                        "2025-07-05", BigDecimal.valueOf(90.00), BigDecimal.valueOf(75.00), true, peakDatesRule),
                createPricingRuleRecordApplyLog(
                        "2025-07-06", BigDecimal.valueOf(100.00), BigDecimal.valueOf(85.00), true, peakDatesRule),
                createPricingRuleRecordApplyLog(
                        "2025-07-07", BigDecimal.valueOf(100.00), BigDecimal.valueOf(85.00), true, peakDatesRule));
    }
}
