package com.moego.client.api.v1.online_booking.utils;

import static org.assertj.core.api.Assertions.assertThat;

import com.google.type.Date;
import com.moego.idl.models.appointment.v1.LodgingAssignAppointmentInfo;
import com.moego.idl.models.appointment.v1.LodgingAssignInfo;
import com.moego.idl.models.appointment.v1.LodgingAssignPetDetailInfo;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.LodgingUnitType;
import com.moego.idl.models.online_booking.v1.CapacityOverrideModel;
import com.moego.idl.models.online_booking.v1.CapacityOverrideUnitType;
import com.moego.idl.models.online_booking.v1.LodgingAvailabilityDef;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;

class LodgingUtilTest {

    @Test
    void calPetCntPerLodgingPerDayWithMultipleAssignments() {
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 1, 3);
        List<LodgingAssignInfo> assignInfoList = List.of(
                LodgingAssignInfo.newBuilder()
                        .setLodgingId(1L)
                        .addAppointments(LodgingAssignAppointmentInfo.newBuilder()
                                .addPetDetails(LodgingAssignPetDetailInfo.newBuilder()
                                        .addSpecificDates("2023-01-01")
                                        .addSpecificDates("2023-01-02")
                                        .setPetId(1)
                                        .build())
                                .build())
                        .build(),
                LodgingAssignInfo.newBuilder()
                        .setLodgingId(2L)
                        .addAppointments(LodgingAssignAppointmentInfo.newBuilder()
                                .addPetDetails(LodgingAssignPetDetailInfo.newBuilder()
                                        .addSpecificDates("2023-01-02")
                                        .addSpecificDates("2023-01-03")
                                        .setPetId(2)
                                        .build())
                                .build())
                        .build(),
                LodgingAssignInfo.newBuilder()
                        .setLodgingId(3L)
                        .addAppointments(LodgingAssignAppointmentInfo.newBuilder()
                                .addPetDetails(LodgingAssignPetDetailInfo.newBuilder()
                                        .setStartDate("2023-01-01")
                                        .setEndDate("2023-01-03")
                                        .setPetId(3)
                                        .build())
                                .build())
                        .build());

        Map<Long, Map<LocalDate, Integer>> result =
                LodgingUtil.calPetCntPerLodgingPerDay(startDate, endDate, assignInfoList);

        assertThat(result)
                .containsExactlyInAnyOrderEntriesOf(Map.of(
                        1L,
                                Map.of(
                                        LocalDate.of(2023, 1, 1), 1,
                                        LocalDate.of(2023, 1, 2), 1),
                        2L,
                                Map.of(
                                        LocalDate.of(2023, 1, 2), 1,
                                        LocalDate.of(2023, 1, 3), 1),
                        3L,
                                Map.of(
                                        LocalDate.of(2023, 1, 1), 1,
                                        LocalDate.of(2023, 1, 2), 1,
                                        LocalDate.of(2023, 1, 3), 1)));
    }

    @Test
    void calPetCntPerLodgingPerDayWithNoAssignments() {
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 1, 3);
        List<LodgingAssignInfo> assignInfoList = List.of();

        Map<Long, Map<LocalDate, Integer>> result =
                LodgingUtil.calPetCntPerLodgingPerDay(startDate, endDate, assignInfoList);

        assertThat(result).isEmpty();
    }

    @Test
    void calPetCntPerLodgingPerDayWithOverlappingDates() {
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 1, 3);
        List<LodgingAssignInfo> assignInfoList = List.of(LodgingAssignInfo.newBuilder()
                .setLodgingId(1L)
                .addAppointments(LodgingAssignAppointmentInfo.newBuilder()
                        .addPetDetails(LodgingAssignPetDetailInfo.newBuilder()
                                .addSpecificDates("2023-01-01")
                                .addSpecificDates("2023-01-02")
                                .setPetId(1)
                                .build())
                        .build())
                .addAppointments(LodgingAssignAppointmentInfo.newBuilder()
                        .addPetDetails(LodgingAssignPetDetailInfo.newBuilder()
                                .addSpecificDates("2023-01-02")
                                .addSpecificDates("2023-01-03")
                                .setPetId(2)
                                .build())
                        .build())
                .build());

        Map<Long, Map<LocalDate, Integer>> result =
                LodgingUtil.calPetCntPerLodgingPerDay(startDate, endDate, assignInfoList);

        assertThat(result)
                .containsExactlyInAnyOrderEntriesOf(Map.of(
                        1L,
                        Map.of(
                                LocalDate.of(2023, 1, 1), 1,
                                LocalDate.of(2023, 1, 2), 2,
                                LocalDate.of(2023, 1, 3), 1)));
    }

    @Test
    void calPetCntPerLodgingPerDayWithEmptyDateRange() {
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 1, 1);
        List<LodgingAssignInfo> assignInfoList = List.of(LodgingAssignInfo.newBuilder()
                .setLodgingId(1L)
                .addAppointments(LodgingAssignAppointmentInfo.newBuilder()
                        .addPetDetails(LodgingAssignPetDetailInfo.newBuilder()
                                .addSpecificDates("2023-01-01")
                                .setPetId(1)
                                .build())
                        .build())
                .build());

        Map<Long, Map<LocalDate, Integer>> result =
                LodgingUtil.calPetCntPerLodgingPerDay(startDate, endDate, assignInfoList);

        assertThat(result).containsExactlyInAnyOrderEntriesOf(Map.of(1L, Map.of(LocalDate.of(2023, 1, 1), 1)));
    }

    @Test
    void isAnyLodgingAvailable_withLimitedCapacity_returnsFalse() {
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 1, 7);
        List<LocalDate> datesToCheck = new ArrayList<>();
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            datesToCheck.add(date);
        }
        List<LodgingTypeModel> lodgingTypes = List.of(
                LodgingTypeModel.newBuilder().setId(1L).setMaxPetNum(1).build(),
                LodgingTypeModel.newBuilder().setId(2L).setMaxPetNum(10).build());

        Map<Long, List<LodgingUnitModel>> lodgingTypeToUnits = Map.of(
                1L,
                        List.of(
                                LodgingUnitModel.newBuilder()
                                        .setId(1L)
                                        .setLodgingTypeId(1L)
                                        .build(),
                                LodgingUnitModel.newBuilder()
                                        .setId(2L)
                                        .setLodgingTypeId(1L)
                                        .build()),
                2L,
                        List.of(LodgingUnitModel.newBuilder()
                                .setId(3L)
                                .setLodgingTypeId(2L)
                                .build()));

        LodgingAvailabilityDef lodgingAvailabilityDef = LodgingAvailabilityDef.newBuilder()
                .setIsCapacityLimited(true)
                .setCapacityLimit(100)
                .build();
        Map<Long, Map<LocalDate, Integer>> petCntPerLodgingPerDay = Map.of(
                1L, Map.of(LocalDate.of(2023, 1, 1), 5),
                2L, Map.of(LocalDate.of(2023, 1, 1), 1));
        Map<LocalDate, Integer> petPendingCntPerDay = Map.of(LocalDate.of(2023, 1, 1), 6);

        // 1.1-1.2 75% percent
        CapacityOverrideModel override1 = CapacityOverrideModel.newBuilder()
                .setUnitType(CapacityOverrideUnitType.PERCENT)
                .setCapacity(75) // 75%
                .addDateRanges(CapacityOverrideModel.CapacityDateRange.newBuilder()
                        .setStartDate(Date.newBuilder()
                                .setYear(2023)
                                .setMonth(1)
                                .setDay(1)
                                .build())
                        .setEndDate(Date.newBuilder()
                                .setYear(2023)
                                .setMonth(1)
                                .setDay(3)
                                .build())
                        .build())
                .build();

        List<CapacityOverrideModel> capacityOverrides = List.of(override1);

        Boolean result = LodgingUtil.isAnyLodgingAvailable(
                datesToCheck,
                1,
                lodgingTypes,
                lodgingTypeToUnits,
                lodgingAvailabilityDef,
                capacityOverrides,
                petCntPerLodgingPerDay,
                petPendingCntPerDay);

        assertThat(result).isFalse();
    }

    @Test
    void isAnyLodgingAvailable_withLimitedCapacity_returnsTrue() {
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 1, 7);
        List<LocalDate> datesToCheck = new ArrayList<>();
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            datesToCheck.add(date);
        }
        List<LodgingTypeModel> lodgingTypes = List.of(
                LodgingTypeModel.newBuilder().setId(1L).setMaxPetNum(1).build(),
                LodgingTypeModel.newBuilder().setId(2L).setMaxPetNum(10).build());

        Map<Long, List<LodgingUnitModel>> lodgingTypeToUnits = Map.of(
                1L,
                        List.of(
                                LodgingUnitModel.newBuilder()
                                        .setId(1L)
                                        .setLodgingTypeId(1L)
                                        .build(),
                                LodgingUnitModel.newBuilder()
                                        .setId(2L)
                                        .setLodgingTypeId(1L)
                                        .build()),
                2L,
                        List.of(LodgingUnitModel.newBuilder()
                                .setId(3L)
                                .setLodgingTypeId(2L)
                                .build()));

        LodgingAvailabilityDef lodgingAvailabilityDef = LodgingAvailabilityDef.newBuilder()
                .setIsCapacityLimited(true)
                .setCapacityLimit(100)
                .build();
        Map<Long, Map<LocalDate, Integer>> petCntPerLodgingPerDay = Map.of(
                1L, Map.of(LocalDate.of(2023, 1, 1), 5),
                2L, Map.of(LocalDate.of(2023, 1, 1), 1));
        Map<LocalDate, Integer> petPendingCntPerDay = Map.of(LocalDate.of(2023, 1, 1), 5);

        Boolean result = LodgingUtil.isAnyLodgingAvailable(
                datesToCheck,
                1,
                lodgingTypes,
                lodgingTypeToUnits,
                lodgingAvailabilityDef,
                List.of(),
                petCntPerLodgingPerDay,
                petPendingCntPerDay);

        assertThat(result).isTrue();
    }

    @Test
    void isAnyLodgingAvailable_withLimitedCapacityAndNoLodging_returnsFalse() {
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 1, 7);
        List<LocalDate> datesToCheck = new ArrayList<>();
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            datesToCheck.add(date);
        }
        List<LodgingTypeModel> lodgingTypes = List.of(
                LodgingTypeModel.newBuilder().setId(1L).setMaxPetNum(1).build(),
                LodgingTypeModel.newBuilder().setId(2L).setMaxPetNum(10).build());

        Map<Long, List<LodgingUnitModel>> lodgingTypeToUnits = Map.of(1L, List.of(), 2L, List.of());

        LodgingAvailabilityDef lodgingAvailabilityDef = LodgingAvailabilityDef.newBuilder()
                .setIsCapacityLimited(true)
                .setCapacityLimit(100)
                .build();
        Map<Long, Map<LocalDate, Integer>> petCntPerLodgingPerDay = Map.of();
        Map<LocalDate, Integer> petPendingCntPerDay = Map.of(LocalDate.of(2023, 1, 1), 0);

        Boolean result = LodgingUtil.isAnyLodgingAvailable(
                datesToCheck,
                1,
                lodgingTypes,
                lodgingTypeToUnits,
                lodgingAvailabilityDef,
                List.of(),
                petCntPerLodgingPerDay,
                petPendingCntPerDay);

        assertThat(result).isFalse();
    }

    @Test
    void isAnyLodgingAvailable_withLimitedCapacityAndMultiPet_returnsFalse() {
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 1, 7);
        List<LocalDate> datesToCheck = new ArrayList<>();
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            datesToCheck.add(date);
        }
        List<LodgingTypeModel> lodgingTypes = List.of(
                LodgingTypeModel.newBuilder().setId(1L).setMaxPetNum(1).build(),
                LodgingTypeModel.newBuilder().setId(2L).setMaxPetNum(10).build());

        Map<Long, List<LodgingUnitModel>> lodgingTypeToUnits = Map.of(
                1L,
                List.of(
                        LodgingUnitModel.newBuilder()
                                .setId(1L)
                                .setLodgingTypeId(1L)
                                .build(),
                        LodgingUnitModel.newBuilder()
                                .setId(2L)
                                .setLodgingTypeId(1L)
                                .build()),
                2L,
                List.of(LodgingUnitModel.newBuilder()
                        .setId(3L)
                        .setLodgingTypeId(2L)
                        .build()));

        LodgingAvailabilityDef lodgingAvailabilityDef = LodgingAvailabilityDef.newBuilder()
                .setIsCapacityLimited(true)
                .setCapacityLimit(100)
                .build();
        Map<Long, Map<LocalDate, Integer>> petCntPerLodgingPerDay = Map.of(
                1L, Map.of(LocalDate.of(2023, 1, 1), 5),
                2L, Map.of(LocalDate.of(2023, 1, 1), 1));
        Map<LocalDate, Integer> petPendingCntPerDay = Map.of(LocalDate.of(2023, 1, 1), 5);

        Boolean result = LodgingUtil.isAnyLodgingAvailable(
                datesToCheck,
                2,
                lodgingTypes,
                lodgingTypeToUnits,
                lodgingAvailabilityDef,
                List.of(),
                petCntPerLodgingPerDay,
                petPendingCntPerDay);

        assertThat(result).isFalse();
    }

    @Test
    void isAnyLodgingAvailable_withUnlimitedCapacity_returnsTrue() {
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 1, 7);
        List<LocalDate> datesToCheck = new ArrayList<>();
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            datesToCheck.add(date);
        }
        List<LodgingTypeModel> lodgingTypes = List.of(
                LodgingTypeModel.newBuilder().setId(1L).setMaxPetNum(1).build(),
                LodgingTypeModel.newBuilder().setId(2L).setMaxPetNum(10).build());

        Map<Long, List<LodgingUnitModel>> lodgingTypeToUnits = Map.of(
                1L,
                        List.of(
                                LodgingUnitModel.newBuilder()
                                        .setId(1L)
                                        .setLodgingTypeId(1L)
                                        .build(),
                                LodgingUnitModel.newBuilder()
                                        .setId(2L)
                                        .setLodgingTypeId(1L)
                                        .build()),
                2L,
                        List.of(LodgingUnitModel.newBuilder()
                                .setId(3L)
                                .setLodgingTypeId(2L)
                                .build()));

        LodgingAvailabilityDef lodgingAvailabilityDef = LodgingAvailabilityDef.newBuilder()
                .setIsCapacityLimited(false)
                .setCapacityLimit(100)
                .build();
        Map<Long, Map<LocalDate, Integer>> petCntPerLodgingPerDay = Map.of(
                1L, Map.of(LocalDate.of(2023, 1, 1), 5),
                2L, Map.of(LocalDate.of(2023, 1, 1), 1));
        Map<LocalDate, Integer> petPendingCntPerDay = Map.of(LocalDate.of(2023, 1, 1), 7);

        Boolean result = LodgingUtil.isAnyLodgingAvailable(
                datesToCheck,
                1,
                lodgingTypes,
                lodgingTypeToUnits,
                lodgingAvailabilityDef,
                List.of(),
                petCntPerLodgingPerDay,
                petPendingCntPerDay);

        assertThat(result).isTrue();
    }

    @Test
    void isAnyLodgingAvailable_withByRoomLodging_returnsFalse() {
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 1, 3);
        List<LocalDate> datesToCheck = new ArrayList<>();
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            datesToCheck.add(date);
        }
        List<LodgingTypeModel> lodgingTypes = List.of(LodgingTypeModel.newBuilder()
                .setId(1L)
                .setMaxPetNum(5)
                .setLodgingUnitType(LodgingUnitType.ROOM)
                .build());

        Map<Long, List<LodgingUnitModel>> lodgingTypeToUnits = Map.of(
                1L,
                List.of(
                        LodgingUnitModel.newBuilder()
                                .setId(1L)
                                .setLodgingTypeId(1L)
                                .build(),
                        LodgingUnitModel.newBuilder()
                                .setId(2L)
                                .setLodgingTypeId(1L)
                                .build()));

        LodgingAvailabilityDef lodgingAvailabilityDef = LodgingAvailabilityDef.newBuilder()
                .setIsCapacityLimited(true)
                .setCapacityLimit(100)
                .build();
        Map<Long, Map<LocalDate, Integer>> petCntPerLodgingPerDay = Map.of(
                1L, Map.of(LocalDate.of(2023, 1, 1), 1, LocalDate.of(2023, 1, 2), 1, LocalDate.of(2023, 1, 3), 1),
                2L, Map.of(LocalDate.of(2023, 1, 1), 1, LocalDate.of(2023, 1, 2), 1, LocalDate.of(2023, 1, 3), 1));
        Map<LocalDate, Integer> petPendingCntPerDay = Map.of(LocalDate.of(2023, 1, 1), 3);

        Boolean result = LodgingUtil.isAnyLodgingAvailable(
                datesToCheck,
                1,
                lodgingTypes,
                lodgingTypeToUnits,
                lodgingAvailabilityDef,
                List.of(),
                petCntPerLodgingPerDay,
                petPendingCntPerDay);

        assertThat(result).isFalse();
    }

    @Test
    void isAnyLodgingAvailable_withMultiLodging_returnsTrue() {
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 1, 3);
        List<LocalDate> datesToCheck = new ArrayList<>();
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            datesToCheck.add(date);
        }
        List<LodgingTypeModel> lodgingTypes = List.of(
                LodgingTypeModel.newBuilder()
                        .setId(1L)
                        .setMaxPetNum(5)
                        .setLodgingUnitType(LodgingUnitType.ROOM)
                        .build(),
                LodgingTypeModel.newBuilder()
                        .setId(2L)
                        .setMaxPetNum(5)
                        .setLodgingUnitType(LodgingUnitType.AREA)
                        .build());

        Map<Long, List<LodgingUnitModel>> lodgingTypeToUnits = Map.of(
                1L,
                List.of(LodgingUnitModel.newBuilder()
                        .setId(1L)
                        .setLodgingTypeId(1L)
                        .build()),
                2L,
                List.of(LodgingUnitModel.newBuilder()
                        .setId(2L)
                        .setLodgingTypeId(2L)
                        .build()));

        LodgingAvailabilityDef lodgingAvailabilityDef = LodgingAvailabilityDef.newBuilder()
                .setIsCapacityLimited(true)
                .setCapacityLimit(100)
                .build();
        Map<Long, Map<LocalDate, Integer>> petCntPerLodgingPerDay = Map.of(
                1L, Map.of(LocalDate.of(2023, 1, 1), 1, LocalDate.of(2023, 1, 2), 1, LocalDate.of(2023, 1, 3), 1),
                2L, Map.of(LocalDate.of(2023, 1, 1), 1, LocalDate.of(2023, 1, 2), 1, LocalDate.of(2023, 1, 3), 1));
        Map<LocalDate, Integer> petPendingCntPerDay = Map.of(LocalDate.of(2023, 1, 1), 3);

        Boolean result = LodgingUtil.isAnyLodgingAvailable(
                datesToCheck,
                1,
                lodgingTypes,
                lodgingTypeToUnits,
                lodgingAvailabilityDef,
                List.of(),
                petCntPerLodgingPerDay,
                petPendingCntPerDay);

        assertThat(result).isTrue();
    }

    @Test
    void isAnyLodgingAvailable_withMultiLodging_returnsFalse() {
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 1, 3);
        List<LocalDate> datesToCheck = new ArrayList<>();
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            datesToCheck.add(date);
        }
        List<LodgingTypeModel> lodgingTypes = List.of(
                LodgingTypeModel.newBuilder()
                        .setId(1L)
                        .setMaxPetNum(5)
                        .setLodgingUnitType(LodgingUnitType.ROOM)
                        .build(),
                LodgingTypeModel.newBuilder()
                        .setId(2L)
                        .setMaxPetNum(5)
                        .setLodgingUnitType(LodgingUnitType.AREA)
                        .build());

        Map<Long, List<LodgingUnitModel>> lodgingTypeToUnits = Map.of(
                1L,
                List.of(LodgingUnitModel.newBuilder()
                        .setId(1L)
                        .setLodgingTypeId(1L)
                        .build()),
                2L,
                List.of(LodgingUnitModel.newBuilder()
                        .setId(2L)
                        .setLodgingTypeId(2L)
                        .build()));

        LodgingAvailabilityDef lodgingAvailabilityDef = LodgingAvailabilityDef.newBuilder()
                .setIsCapacityLimited(true)
                .setCapacityLimit(100)
                .build();
        Map<Long, Map<LocalDate, Integer>> petCntPerLodgingPerDay = Map.of(
                1L, Map.of(LocalDate.of(2023, 1, 1), 1, LocalDate.of(2023, 1, 2), 1, LocalDate.of(2023, 1, 3), 1),
                2L, Map.of(LocalDate.of(2023, 1, 1), 5, LocalDate.of(2023, 1, 2), 5, LocalDate.of(2023, 1, 3), 5));
        Map<LocalDate, Integer> petPendingCntPerDay = Map.of(LocalDate.of(2023, 1, 1), 3);

        Boolean result = LodgingUtil.isAnyLodgingAvailable(
                datesToCheck,
                1,
                lodgingTypes,
                lodgingTypeToUnits,
                lodgingAvailabilityDef,
                List.of(),
                petCntPerLodgingPerDay,
                petPendingCntPerDay);

        assertThat(result).isFalse();
    }

    @Test
    void getTotalLodgingPetCountByDate_boundaryCase_handlesEmptyValues() {
        List<LocalDate> dates = List.of(LocalDate.of(2023, 5, 1), LocalDate.of(2023, 5, 2));
        List<CapacityOverrideModel> capacityOverrides = List.of();
        List<LodgingTypeModel> lodgingTypes = List.of();
        Map<Long, List<LodgingUnitModel>> lodgingTypeToUnits = Map.of();
        int defaultCapacityLimit = 100;

        Map<LocalDate, Integer> result = LodgingUtil.getTotalLodgingPetCountByDate(
                dates, capacityOverrides, lodgingTypes, lodgingTypeToUnits, defaultCapacityLimit);

        // 验证结果 - 所有日期应该有零容量
        assertThat(result).hasSize(2);
        assertThat(result.get(LocalDate.of(2023, 5, 1))).isEqualTo(0);
        assertThat(result.get(LocalDate.of(2023, 5, 2))).isEqualTo(0);
    }

    @Test
    void getTotalLodgingPetCountByDate_complexCase_handlesMultipleTypesAndOverrides() {
        // 准备测试数据 - 复杂场景
        List<LocalDate> dates = List.of(
                LocalDate.of(2023, 5, 1), // 有PERCENT覆盖
                LocalDate.of(2023, 5, 2), // 有PERCENT覆盖(被另一规则覆盖)
                LocalDate.of(2023, 5, 3), // 有PET类型覆盖
                LocalDate.of(2023, 5, 4), // 无覆盖
                LocalDate.of(2023, 5, 5) // 无覆盖
                );

        // 创建多个覆盖规则
        // 5.1-5.2 75% percent
        CapacityOverrideModel override1 = CapacityOverrideModel.newBuilder()
                .setUnitType(CapacityOverrideUnitType.PERCENT)
                .setCapacity(75) // 75%
                .addDateRanges(CapacityOverrideModel.CapacityDateRange.newBuilder()
                        .setStartDate(Date.newBuilder()
                                .setYear(2023)
                                .setMonth(5)
                                .setDay(1)
                                .build())
                        .setEndDate(Date.newBuilder()
                                .setYear(2023)
                                .setMonth(5)
                                .setDay(2)
                                .build())
                        .build())
                .build();
        // 5.2 50% percent(验证日期重复的情况)
        CapacityOverrideModel override2 = CapacityOverrideModel.newBuilder()
                .setUnitType(CapacityOverrideUnitType.PERCENT)
                .setCapacity(50) // 50%
                .addDateRanges(CapacityOverrideModel.CapacityDateRange.newBuilder()
                        .setStartDate(Date.newBuilder()
                                .setYear(2023)
                                .setMonth(5)
                                .setDay(2)
                                .build())
                        .setEndDate(Date.newBuilder()
                                .setYear(2023)
                                .setMonth(5)
                                .setDay(2)
                                .build())
                        .build())
                .build();
        // 5.3 3 pets
        CapacityOverrideModel override3 = CapacityOverrideModel.newBuilder()
                .setUnitType(CapacityOverrideUnitType.PET)
                .setCapacity(3) // 每单位3宠物
                .addDateRanges(CapacityOverrideModel.CapacityDateRange.newBuilder()
                        .setStartDate(Date.newBuilder()
                                .setYear(2023)
                                .setMonth(5)
                                .setDay(3)
                                .build())
                        .setEndDate(Date.newBuilder()
                                .setYear(2023)
                                .setMonth(5)
                                .setDay(3)
                                .build())
                        .build())
                .build();

        List<CapacityOverrideModel> capacityOverrides = List.of(override1, override2, override3);

        List<LodgingTypeModel> lodgingTypes = List.of(
                LodgingTypeModel.newBuilder().setId(1L).setMaxPetNum(4).build(),
                LodgingTypeModel.newBuilder().setId(2L).setMaxPetNum(6).build(),
                LodgingTypeModel.newBuilder().setId(3L).setMaxPetNum(5).build() // empty unit
                );

        Map<Long, List<LodgingUnitModel>> lodgingTypeToUnits = Map.of(
                1L,
                        List.of(
                                LodgingUnitModel.newBuilder()
                                        .setId(1L)
                                        .setLodgingTypeId(1L)
                                        .build(),
                                LodgingUnitModel.newBuilder()
                                        .setId(2L)
                                        .setLodgingTypeId(1L)
                                        .build()),
                2L,
                        List.of(LodgingUnitModel.newBuilder()
                                .setId(3L)
                                .setLodgingTypeId(2L)
                                .build()),
                3L, List.of() // 没有单位
                );

        int defaultCapacityLimit = 100; // 100%

        // 执行测试
        Map<LocalDate, Integer> result = LodgingUtil.getTotalLodgingPetCountByDate(
                dates, capacityOverrides, lodgingTypes, lodgingTypeToUnits, defaultCapacityLimit);

        // 验证结果
        // pet total capacity base：(4pet*2unit + 6pet*1unit + 5pet*0unit) = 8 + 6 + 0 = 14
        // 5月1日：14 * 75% / 100 = 10.5 -> 10
        // 5月2日：14 * 50% / 100 = 7
        // 5月3日：3 pet
        // 5月4日和5月5日：14 * 100% / 100 = 14
        assertThat(result).hasSize(5);
        assertThat(result.get(LocalDate.of(2023, 5, 1))).isEqualTo(10);
        assertThat(result.get(LocalDate.of(2023, 5, 2))).isEqualTo(7);
        assertThat(result.get(LocalDate.of(2023, 5, 3))).isEqualTo(3);
        assertThat(result.get(LocalDate.of(2023, 5, 4))).isEqualTo(14);
        assertThat(result.get(LocalDate.of(2023, 5, 5))).isEqualTo(14);
    }

    @Test
    void getLodgingCapacity_withNoOverride_returnsExpectedCapacity() {
        List<LodgingTypeModel> lodgingTypes = List.of(
                LodgingTypeModel.newBuilder().setId(1L).setMaxPetNum(2).build(),
                LodgingTypeModel.newBuilder().setId(2L).setMaxPetNum(5).build());

        Map<Long, List<LodgingUnitModel>> lodgingTypeToUnits = Map.of(
                1L,
                        List.of(
                                LodgingUnitModel.newBuilder()
                                        .setId(1L)
                                        .setLodgingTypeId(1L)
                                        .build(),
                                LodgingUnitModel.newBuilder()
                                        .setId(2L)
                                        .setLodgingTypeId(1L)
                                        .build()),
                2L,
                        List.of(LodgingUnitModel.newBuilder()
                                .setId(3L)
                                .setLodgingTypeId(2L)
                                .build()));

        int lodgingCapacityLimit = 150; // 150%

        int result = LodgingUtil.getLodgingCapacity(lodgingTypes, lodgingTypeToUnits, null, lodgingCapacityLimit);

        // 验证结果：(2pet * 2unit + 5pet * 1unit) * 150% / 100 = (4 + 5) * 1.5 = 13.5 -> 13
        var expected = 13;
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getLodgingCapacity_withMixedScenario_returnsCorrectTotal() {
        // 准备测试数据 - 一些类型有lodging unit，一些lodging unit
        List<LodgingTypeModel> lodgingTypes = List.of(
                LodgingTypeModel.newBuilder().setId(1L).setMaxPetNum(3).build(),
                LodgingTypeModel.newBuilder().setId(2L).setMaxPetNum(5).build(),
                LodgingTypeModel.newBuilder().setId(3L).setMaxPetNum(2).build());

        Map<Long, List<LodgingUnitModel>> lodgingTypeToUnits = Map.of(
                1L,
                List.of(
                        LodgingUnitModel.newBuilder()
                                .setId(1L)
                                .setLodgingTypeId(1L)
                                .build(),
                        LodgingUnitModel.newBuilder()
                                .setId(2L)
                                .setLodgingTypeId(1L)
                                .build()),
                2L,
                List.of(), // empty unit
                3L,
                List.of(LodgingUnitModel.newBuilder()
                        .setId(3L)
                        .setLodgingTypeId(3L)
                        .build()));

        CapacityOverrideModel capacityOverride = CapacityOverrideModel.newBuilder()
                .setUnitType(CapacityOverrideUnitType.PERCENT)
                .setCapacity(75) // 75%
                .build();

        int lodgingCapacityLimit = 100;

        int result = LodgingUtil.getLodgingCapacity(
                lodgingTypes, lodgingTypeToUnits, capacityOverride, lodgingCapacityLimit);

        // 验证结果：(3pet * 2unit + 5pet * 0unit + 2pet * 1unit) * 75% / 100 = (6 + 0 + 2) * 0.75 = 6
        var expected = 6;
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getLodgingCapacity_withPetTypeOverride_returnsPetBasedCapacity() {
        List<LodgingTypeModel> lodgingTypes = List.of(
                LodgingTypeModel.newBuilder().setId(1L).setMaxPetNum(2).build(),
                LodgingTypeModel.newBuilder().setId(2L).setMaxPetNum(5).build());

        Map<Long, List<LodgingUnitModel>> lodgingTypeToUnits = Map.of(
                1L,
                        List.of(
                                LodgingUnitModel.newBuilder()
                                        .setId(1L)
                                        .setLodgingTypeId(1L)
                                        .build(),
                                LodgingUnitModel.newBuilder()
                                        .setId(2L)
                                        .setLodgingTypeId(1L)
                                        .build()),
                2L,
                        List.of(LodgingUnitModel.newBuilder()
                                .setId(3L)
                                .setLodgingTypeId(2L)
                                .build()));

        CapacityOverrideModel capacityOverride = CapacityOverrideModel.newBuilder()
                .setUnitType(CapacityOverrideUnitType.PET)
                .setCapacity(3) // 固定3只宠物
                .build();

        int lodgingCapacityLimit = 150;

        int result = LodgingUtil.getLodgingCapacity(
                lodgingTypes, lodgingTypeToUnits, capacityOverride, lodgingCapacityLimit);

        // 验证结果：3pet
        var excepted = 3;
        assertThat(result).isEqualTo(excepted);
    }

    @Test
    void getLodgingCapacity_withZeroLimitAndNoOverride_returnsZero() {
        List<LodgingTypeModel> lodgingTypes =
                List.of(LodgingTypeModel.newBuilder().setId(1L).setMaxPetNum(2).build());

        Map<Long, List<LodgingUnitModel>> lodgingTypeToUnits = Map.of(
                1L,
                List.of(LodgingUnitModel.newBuilder()
                        .setId(1L)
                        .setLodgingTypeId(1L)
                        .build()));

        int lodgingCapacityLimit = 0; // 0%容量限制

        int result = LodgingUtil.getLodgingCapacity(lodgingTypes, lodgingTypeToUnits, null, lodgingCapacityLimit);

        assertThat(result).isEqualTo(0);
    }
}
