<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<!-- use mbGenerator: https://moego.atlassian.net/wiki/spaces/~************************/pages/79233368 -->
<generatorConfiguration>
    <context id="Mysql" targetRuntime="MyBatis3" defaultModelType="flat">
        <property name="javaFileEncoding" value="UTF-8"/>
        <property name="beginningDelimiter" value='`'/>
        <property name="endingDelimiter" value='`'/>
        <plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin"/>
        <plugin type="com.softwareloop.mybatis.generator.plugins.LombokPlugin">
            <property name="builder" value="true"/>
            <property name="builder.toBuilder" value="true"/>
            <property name="noArgsConstructor" value="true"/>
            <property name="noArgsConstructor.staticName" value="&quot;&quot;"/>
            <property name="allArgsConstructor" value="true"/>
            <property name="allArgsConstructor.staticName" value="&quot;&quot;"/>
        </plugin>
        <commentGenerator>
            <property name="suppressDate" value="true"/>
        </commentGenerator>
        <!-- 本服务数据存储与 moego-server-business 共用，MySQL 的 moe_business 库 -->
        <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
                        connectionURL="***************************************************************"
                        userId="moego_developer_240310_eff7a0dc"
                        password="G0MxI7NM_jX_f7Ky73vnrwej97xg1tly">
            <property name="nullCatalogMeansCurrent" value="true"/>
        </jdbcConnection>
        <javaTypeResolver>
            <property name="useJSR310Types" value="true"/>
        </javaTypeResolver>
        <javaModelGenerator targetPackage="com.moego.svc.organization.entity" targetProject="src/main/java">
            <property name="annotations" value="@lombok.NoArgsConstructor,@lombok.AllArgsConstructor"/>
        </javaModelGenerator>
        <sqlMapGenerator targetPackage="mapper.base" targetProject="src/main/resources"/>
        <javaClientGenerator
                targetPackage="com.moego.svc.organization.mapper.base"
                targetProject="src/main/java"
                type="XMLMAPPER"
        />


<!--        <table tableName="moe_business" mapperName="BaseMoeBusinessMapper">-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--        </table>-->
<!--        <table tableName="moe_van" mapperName="BaseMoeVanMapper">-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--        </table>-->
<!--        <table tableName="moe_staff" mapperName="BaseMoeStaffMapper">-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--            <columnOverride column="access_all_working_locations_staff" javaType="Byte" jdbcType="TINYINT"/>-->
<!--            &lt;!&ndash; 废弃字段 &ndash;&gt;-->
<!--            <ignoreColumn column="pay_by"/>-->
<!--            <ignoreColumn column="service_pay_rate"/>-->
<!--            <ignoreColumn column="addon_pay_rate"/>-->
<!--            <ignoreColumn column="hourly_pay_rate"/>-->
<!--            <ignoreColumn column="tips_pay_rate"/>-->
<!--        </table>-->
<!--        <table tableName="moe_van_staff" mapperName="BaseMoeVanStaffMapper">-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--        </table>-->
<!--        <table tableName="moe_staff_appointment" mapperName="BaseMoeStaffAppointmentMapper">-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--        </table>-->

<!--        <table tableName="moe_staff_tracking" mapperName="BaseMoeStaffTrackingMapper">-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--        </table>-->
<!--        <table tableName="moe_staff_override_date" mapperName="BaseMoeStaffOverrideDate">-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--        </table>-->

<!--        <table tableName="moe_business_payment_method" mapperName="BasePaymentMethodMapper">-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--        </table>-->
<!--        <table tableName="moe_business_tax" mapperName="BaseMoeBusinessTaxMapper">-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--        </table>-->
<!--        <table tableName="moe_business_working_hour" mapperName="BaseMoeBusinessWorkingHourMapper">-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--        </table>-->
<!--        <table tableName="moe_business_close_date" mapperName="BaseMoeBusinessCloseDateMapper">-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--        </table>-->
<!--        <table tableName="moe_company" mapperName="BaseMoeCompanyMapper">-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--            <columnOverride column="notification_sound_enable" javaType="java.lang.Boolean"/>-->
<!--        </table>-->
<!--        <table tableName="moe_staff_notification" mapperName="BaseMoeStaffNotificationMapper">-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--        </table>-->

<!--        <table tableName="moe_staff_working_location" mapperName="BaseMoeStaffWorkingLocationMapper">-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--        </table>-->

<!--        <table tableName="moe_staff_working_hour" mapperName="BaseMoeStaffWorkingHourMapper">-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--        </table>-->

<!--        <table tableName="moe_business_payroll_setting" mapperName="BaseMoeBusinessPayrollSettingMapper">-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--            <ignoreColumn column="tips_commission_based"/> &lt;!&ndash; 经过 Rich/Chris/PQ 三方确认，这个字段没用到 &ndash;&gt;-->
<!--        </table>-->

<!--        <table tableName="moe_staff_payroll_setting" mapperName="BaseMoeStaffPayrollSettingMapper">-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--        </table>-->

<!--        <table tableName="moe_payroll_exception" mapperName="BaseMoePayrollExceptionMapper">-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--        </table>-->

<!--        <table tableName="moe_clock_in_out_setting" mapperName="BaseMoeClockInOutSettingMapper">-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--        </table>-->

<!--        <table tableName="moe_business_clock_in_out_log" mapperName="BaseMoeBusinessClockInOutLogMapper">-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--        </table>-->

<!--        <table tableName="moe_account_company_preference" mapperName="BaseMoeAccountCompanyPreferenceMapper">-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--        </table>-->
<!--        <table tableName="staff_availability" mapperName="BaseStaffAvailability">-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--        </table>-->
<!--        <table tableName="staff_availability_slot_day" mapperName="BaseStaffAvailabilitySlotDay">-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--            <columnOverride column="limit_ids"-->
<!--                            javaType="java.util.List&lt;Long&gt;"-->
<!--                            typeHandler="com.moego.svc.organization.mapper.typehander.JsonLongListTypeHandler"/>-->
<!--        </table>-->
        <table tableName="staff_availability_time_day" mapperName="BaseStaffAvailabilityTimeDay">
            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>
            <columnOverride column="limit_ids"
                            javaType="java.util.List&lt;Long&gt;"
                            typeHandler="com.moego.svc.organization.mapper.typehander.JsonLongListTypeHandler"/>
        </table>
<!--        <table tableName="staff_availability_day_hour" mapperName="BaseStaffAvailabilityDayHour">-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--            <columnOverride column="limit_ids"-->
<!--                            javaType="java.util.List&lt;Long&gt;"-->
<!--                            typeHandler="com.moego.svc.organization.mapper.typehander.JsonLongListTypeHandler"/>-->

<!--        </table>-->
        <table tableName="day_hour_limit" mapperName="BaseDayHourLimit">
            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>
            <columnOverride column="service_ids"
                            javaType="java.util.List&lt;Long&gt;"
                            typeHandler="com.moego.svc.organization.mapper.typehander.JsonLongListTypeHandler"/>
            <columnOverride column="breed_ids"
                            javaType="java.util.List&lt;Long&gt;"
                            typeHandler="com.moego.svc.organization.mapper.typehander.JsonLongListTypeHandler"/>
            <columnOverride column="pet_size_ids"
                            javaType="java.util.List&lt;Long&gt;"
                            typeHandler="com.moego.svc.organization.mapper.typehander.JsonLongListTypeHandler"/>
        </table>
<!--        <table tableName="idogcam_config" mapperName="BaseIdogcamConfigMapper">-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--        </table>-->
<!--        <table tableName="abckam_config" mapperName="BaseAbckamConfigMapper">-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--        </table>-->
<!--        <table tableName="camera" mapperName="BaseCameraMapper">-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--        </table>-->

        <table tableName="day_hour_limit_group" mapperName="BaseDayHourLimitGroupMapper">
            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>
        </table>
    </context>
</generatorConfiguration>
