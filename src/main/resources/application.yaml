spring:
  application:
    name: moego-svc-appointment
  cloud:
    aws:
      region:
        static: ${AWS_REGION:us-west-2}
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${secret.datasource.grooming.mysql.url}:${secret.datasource.grooming.mysql.port}/moe_grooming?useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=false
    username: ${secret.datasource.grooming.mysql.username}
    password: ${secret.datasource.grooming.mysql.password}
  additional-datasources:
    - name: postgres
      driver-class-name: org.postgresql.Driver
      url: jdbc:postgresql://${secret.datasource.default.postgres.url}:${secret.datasource.default.postgres.port}/moego_appointment
      username: ${secret.datasource.default.postgres.moego_appointment.username}
      password: ${secret.datasource.default.postgres.moego_appointment.password}
  activemq:
    broker-url: ${secret.mq.activemq.url}
    password: ${secret.mq.activemq.password}
    user: ${secret.mq.activemq.user}
    enabled: true
  profiles:
    active: local
  data:
    redis:
      host: ${secret.redis.host}
      password: ${secret.redis.password}
      port: ${secret.redis.port}
      ssl:
        enabled: ${secret.redis.tls}
      timeout: 60000
      key:
        delimiter: ':'
        prefix: local
mybatis:
  configuration:
    cache-enabled: false

moego:
  messaging:
    enabled: true
    pulsar:
      service-url: ${secret.mq.pulsar.service_url}
      authentication: ${secret.mq.pulsar.token}
      tenant: test2
  event-bus:
    brokers:
      - name: default
        addresses:
          - ${secret.mq.kafka.broker_url_0}
          - ${secret.mq.kafka.broker_url_1}
          - ${secret.mq.kafka.broker_url_2}
        security:
          enabled: true
          properties:
            security.protocol: SASL_SSL
            sasl.mechanism: AWS_MSK_IAM
            sasl.jaas.config: software.amazon.msk.auth.iam.IAMLoginModule required;
            sasl.client.callback.handler.class: software.amazon.msk.auth.iam.IAMClientCallbackHandler
    producer:
      enabled: true
      log-success: false
      log-failure: true
    consumer:
      enabled: true
  server:
    url:
      customer: moego-service-customer:9201
      business: moego-service-business:9203
      payment: moego-service-payment:9204
      message: moego-service-message:9205
      grooming: moego-service-grooming:9206
      retail: moego-service-retail:9207
  grpc:
    server:
      empty-server-enabled: true
      debug-enabled: true
      port: 9090
    client:
      stubs:
        - service: moego.service.offering.**
          authority: moego-svc-offering:9090
        - service: moego.service.order.**
          authority: moego-svc-order:9090
        - service: moego.service.permission.**
          authority: moego-svc-permission:9090
        - service: moego.service.business_customer.**
          authority: moego-svc-business-customer:9090
        - service: moego.service.agreement.**
          authority: moego-svc-agreement:9090
        - service: moego.service.organization.**
          authority: moego-svc-organization:9090
        - service: moego.service.notification.**
          authority: moego-svc-notification:9090
        - service: moego.service.account.**
          authority: moego-svc-account:9090
        - service: moego.service.activity_log.**
          authority: moego-svc-activity-log:9090
        - service: moego.service.membership.**
          authority: moego-svc-membership:9090
        - service: moego.service.metadata.**
          authority: moego-svc-metadata:9090
        - service: moego.service.online_booking.**
          authority: moego-svc-online-booking:9090
        - service: moego.service.map.v1.*
          authority: moego-svc-map:9090
        - service: moego.service.payment.v2.*
          authority: moego-svc-payment:9090
        - service: moego.service.reporting.v2.*
          authority: moego-svc-reporting-v2:9090
        - service: moego.service.branded_app.v1.**
          authority: moego-svc-branded-app:9090
  daily-report:
    client-url: https://client.t2.moego.dev/daily/report/%s
  feature-flag:
    growth-book:
      api-host: ${secret.growthbook.host}
      client-key: ${secret.growthbook.client_key}
