package com.moego.client.api.v1.online_booking.utils;

import com.google.type.Date;
import com.moego.client.api.v1.converter.DateConverter;
import com.moego.idl.models.appointment.v1.LodgingAssignAppointmentInfo;
import com.moego.idl.models.appointment.v1.LodgingAssignInfo;
import com.moego.idl.models.appointment.v1.LodgingAssignPetDetailInfo;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.LodgingUnitType;
import com.moego.idl.models.online_booking.v1.BoardingServiceDetailModel;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.CapacityOverrideModel;
import com.moego.idl.models.online_booking.v1.CapacityOverrideUnitType;
import com.moego.idl.models.online_booking.v1.DaycareServiceDetailModel;
import com.moego.idl.models.online_booking.v1.LodgingAvailabilityDef;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.springframework.util.CollectionUtils;

public class LodgingUtil {

    // return <key: lodgingUnitId, value: <key: date, value: petCnt>>
    public static Map<Long, Map<LocalDate, Integer>> calPetCntPerLodgingPerDay(
            LocalDate startDate, LocalDate endDate, List<LodgingAssignInfo> assignInfoList) {
        // <key: lodgingId, value: <key: date, value: Set<petId>>>
        Map<Long, Map<LocalDate, Set<Integer>>> petsPerLodgingPerDay = new HashMap<>();

        // 收集 pet 寄养信息
        Set<LocalDate> datesToCollect = Stream.iterate(
                        startDate, date -> !date.isAfter(endDate), date -> date.plusDays(1))
                .collect(Collectors.toSet());
        for (LodgingAssignInfo assignInfo : assignInfoList) {
            Map<LocalDate, Set<Integer>> petPerDay = new HashMap<>();

            for (LodgingAssignAppointmentInfo appointmentInfo : assignInfo.getAppointmentsList()) {
                appointmentInfo.getPetDetailsList().forEach(k -> collectPetDetailDates(k).stream()
                        .filter(datesToCollect::contains)
                        .forEach(date -> petPerDay
                                .computeIfAbsent(date, v -> new HashSet<>())
                                .add(k.getPetId())));

                appointmentInfo
                        .getPetEvaluationsList()
                        .forEach(k -> generateAllDatesBetween(k.getStartDate(), k.getEndDate()).stream()
                                .filter(datesToCollect::contains)
                                .forEach(date -> petPerDay
                                        .computeIfAbsent(date, v -> new HashSet<>())
                                        .add(k.getPetId())));
            }
            petsPerLodgingPerDay.put(assignInfo.getLodgingId(), petPerDay);
        }

        Map<Long, Map<LocalDate, Integer>> result = new HashMap<>();
        petsPerLodgingPerDay.forEach((lodgingId, petDateMap) -> {
            Map<LocalDate, Integer> petCntMap = new HashMap<>();
            petDateMap.forEach((date, petSet) -> petCntMap.put(date, petSet.size()));
            result.put(lodgingId, petCntMap);
        });
        return result;
    }

    // 收集 pet service detail 占用的 lodging 排期信息
    private static List<LocalDate> collectPetDetailDates(LodgingAssignPetDetailInfo petDetail) {
        if (!CollectionUtils.isEmpty(petDetail.getSpecificDatesList())) {
            return petDetail.getSpecificDatesList().stream()
                    .map(LocalDate::parse)
                    .toList();
        }
        return generateAllDatesBetween(petDetail.getStartDate(), petDetail.getEndDate());
    }

    private static List<LocalDate> generateAllDatesBetween(String startDate, String endDate) {
        LocalDate start = LocalDate.parse(startDate);
        LocalDate end = LocalDate.parse(endDate);
        return Stream.iterate(start, date -> !date.isAfter(end), date -> date.plusDays(1))
                .toList();
    }

    private static List<LocalDate> generateAllDatesBetween(Date startDate, Date endDate) {
        LocalDate start = DateConverter.INSTANCE.toLocalDate(startDate);
        LocalDate end = DateConverter.INSTANCE.toLocalDate(endDate);
        return Stream.iterate(start, date -> !date.isAfter(end), date -> date.plusDays(1))
                .toList();
    }

    // return <key: serviceId, value: <key: date, value: petCnt>>
    public static Map<Long, Map<LocalDate, Integer>> calPetCntPerService(
            LocalDate startDate, LocalDate endDate, List<BookingRequestModel> bookingRequests) {
        // <key: serviceId, value: <key: date, value: Set<petId>>>
        Map<Long, Map<LocalDate, Set<Long>>> petsPerServicePerDay = new HashMap<>();

        // 收集 pet 寄养信息
        Set<LocalDate> datesToCollect = Stream.iterate(
                        startDate, date -> !date.isAfter(endDate), date -> date.plusDays(1))
                .collect(Collectors.toSet());
        for (BookingRequestModel bookingRequest : bookingRequests) {
            bookingRequest.getServicesList().forEach(service -> {
                if (service.hasBoarding()) {
                    BoardingServiceDetailModel petService =
                            service.getBoarding().getService();
                    generateAllDatesBetween(petService.getStartDate(), petService.getEndDate()).stream()
                            .filter(datesToCollect::contains)
                            .forEach(date -> petsPerServicePerDay
                                    .computeIfAbsent(petService.getServiceId(), v -> new HashMap<>())
                                    .computeIfAbsent(date, v -> new HashSet<>())
                                    .add(petService.getPetId()));
                }
                if (service.hasDaycare()) {
                    DaycareServiceDetailModel petService = service.getDaycare().getService();
                    petService.getSpecificDatesList().stream()
                            .map(LocalDate::parse)
                            .filter(datesToCollect::contains)
                            .forEach(date -> petsPerServicePerDay
                                    .computeIfAbsent(petService.getServiceId(), v -> new HashMap<>())
                                    .computeIfAbsent(date, v -> new HashSet<>())
                                    .add(petService.getPetId()));
                }
            });
        }

        Map<Long, Map<LocalDate, Integer>> result = new HashMap<>();
        petsPerServicePerDay.forEach((lodgingId, petDateMap) -> {
            Map<LocalDate, Integer> petCntMap = new HashMap<>();
            petDateMap.forEach((date, petSet) -> petCntMap.put(date, petSet.size()));
            result.put(lodgingId, petCntMap);
        });
        return result;
    }

    /**
     * 判断在若干房型下是否有在一段时间均可用的 lodging
     * @param dates 判断是否可用需要检查的日期
     * @param newPetsCount 新增需要容纳的 pet 数量
     * @param petCntPerLodgingPerDay 已有预约，lodging 每天的使用情况
     * @param petPendingCntPerDay 已有 ob request，等待分配 lodging 的 pet 数量
     */
    public static Boolean isAnyLodgingAvailable(
            List<LocalDate> dates,
            Integer newPetsCount,
            List<LodgingTypeModel> lodgingTypes,
            Map<Long, List<LodgingUnitModel>> lodgingTypeToUnits,
            LodgingAvailabilityDef lodgingAvailabilityDef,
            List<CapacityOverrideModel> capacityOverrides,
            Map<Long, Map<LocalDate, Integer>> petCntPerLodgingPerDay,
            Map<LocalDate, Integer> petPendingCntPerDay) {

        if (!lodgingAvailabilityDef.getIsCapacityLimited()) {
            return true;
        }

        Map<LocalDate, Integer> totalLodgingPetCountByDate = getTotalLodgingPetCountByDate(
                dates, capacityOverrides, lodgingTypes, lodgingTypeToUnits, lodgingAvailabilityDef.getCapacityLimit());

        // 检查每一天是否都有足够容量
        for (LocalDate date : dates) {
            var totalCapacityLimit = totalLodgingPetCountByDate.get(date);
            if (totalCapacityLimit == null) {
                return false;
            }

            totalCapacityLimit -= newPetsCount;
            if (totalCapacityLimit < 0) {
                return false;
            }

            int totalExistPetCnt = petPendingCntPerDay.getOrDefault(date, 0);
            for (LodgingTypeModel lodgingType : lodgingTypes) {
                for (LodgingUnitModel unit : lodgingTypeToUnits.getOrDefault(lodgingType.getId(), List.of())) {
                    Map<LocalDate, Integer> petCountMap = petCntPerLodgingPerDay.getOrDefault(unit.getId(), Map.of());
                    Integer unitExistPetCnt = petCountMap.getOrDefault(date, 0);

                    // by room 只有在有房间为空的情况下才算 available，无论里面还剩余多少容量
                    if (Objects.equals(lodgingType.getLodgingUnitType(), LodgingUnitType.ROOM)) {
                        if (unitExistPetCnt > 0) {
                            totalExistPetCnt += Math.max(lodgingType.getMaxPetNum(), unitExistPetCnt); // 按最大容纳能力计算
                        } else {
                            // 有房间为空，则该 lodging type 已经 available
                            break;
                        }
                    } else { // 默认为 by area 的计算逻辑
                        totalExistPetCnt += unitExistPetCnt;
                    }

                    if (totalExistPetCnt > totalCapacityLimit) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    /**
     * 基于 capacity override 按天维度计算 ob 所有 lodging capacity 的总和
     *
     * @param dates 需要计算的日期
     * @param lodgingTypes lodging type list
     * @param lodgingTypeToUnits lodging type to units map
     * @param capacityOverrides capacity override list
     * @param lodgingCapacityLimit ob setting 中的 lodging capacity limit
     * @return 计算后的总容量
     */
    public static Map<LocalDate, Integer> getTotalLodgingPetCountByDate(
            List<LocalDate> dates,
            List<CapacityOverrideModel> capacityOverrides,
            List<LodgingTypeModel> lodgingTypes,
            Map<Long, List<LodgingUnitModel>> lodgingTypeToUnits,
            int lodgingCapacityLimit) {
        if (CollectionUtils.isEmpty(capacityOverrides)) {
            capacityOverrides = List.of();
        }

        Map<LocalDate, CapacityOverrideModel> capacityOverrideDateMap = capacityOverrides.stream()
                .map(capacityOverride -> {
                    Set<LocalDate> localDates = capacityOverride.getDateRangesList().stream()
                            .map(dateRange -> generateAllDatesBetween(dateRange.getStartDate(), dateRange.getEndDate()))
                            .flatMap(List::stream)
                            .collect(Collectors.toSet());
                    return localDates.stream().collect(Collectors.toMap(date -> date, date -> capacityOverride));
                })
                .reduce(new HashMap<>(), (a, b) -> {
                    a.putAll(b);
                    return a;
                });

        Map<LocalDate, Integer> result = new HashMap<>();
        for (LocalDate date : dates) {
            CapacityOverrideModel capacityOverrideModel = capacityOverrideDateMap.get(date);
            int capacity =
                    getLodgingCapacity(lodgingTypes, lodgingTypeToUnits, capacityOverrideModel, lodgingCapacityLimit);
            result.put(date, capacity);
        }

        return result;
    }

    /**
     * 基于 capacity override 计算 ob 所有 lodging capacity 的总和
     *
     * @param lodgingTypes lodging type list
     * @param lodgingTypeToUnits lodging type to units map
     * @param capacityOverrideModel capacity override 配置(allow null)
     * @param lodgingCapacityLimit ob setting 中的 lodging capacity limit
     * @return 计算后的总容量
     */
    public static int getLodgingCapacity(
            List<LodgingTypeModel> lodgingTypes,
            Map<Long, List<LodgingUnitModel>> lodgingTypeToUnits,
            CapacityOverrideModel capacityOverrideModel,
            int lodgingCapacityLimit) {
        if (CollectionUtils.isEmpty(lodgingTypes)) {
            return 0;
        }

        // per pet 表示当日的 capacity 总共有多少个 pet
        if (Objects.nonNull(capacityOverrideModel)
                && capacityOverrideModel.getUnitType().equals(CapacityOverrideUnitType.PET)) {
            return capacityOverrideModel.getCapacity();
        }

        return lodgingTypes.stream()
                        .mapToInt(k -> {
                            int unitCount = lodgingTypeToUnits
                                    .getOrDefault(k.getId(), List.of())
                                    .size();
                            if (Objects.nonNull(capacityOverrideModel)) {
                                return k.getMaxPetNum() * unitCount * capacityOverrideModel.getCapacity();
                            } else {
                                return k.getMaxPetNum() * unitCount * lodgingCapacityLimit;
                            }
                        })
                        .sum()
                / 100; // /100% to get the actual number
    }
}
