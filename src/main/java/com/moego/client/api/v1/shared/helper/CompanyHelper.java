package com.moego.client.api.v1.shared.helper;

import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.organization.v1.CompanyPreferenceSettingModel;
import com.moego.idl.service.organization.v1.CompanyServiceGrpc;
import com.moego.idl.service.organization.v1.GetCompanyPreferenceSettingRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/2/8
 */
@Component
@RequiredArgsConstructor
public class CompanyHelper {

    private final CompanyServiceGrpc.CompanyServiceBlockingStub companyStub;

    /**
     * Get {@link LocalDateTime} in company's timezone, throw exception if company not found.
     *
     * @param companyId company id
     * @return LocalDateTime in company's timezone
     */
    public LocalDateTime mustGetCompanyDateTime(long companyId) {
        var resp = companyStub.getCompanyPreferenceSetting(GetCompanyPreferenceSettingRequest.newBuilder()
                .setCompanyId(companyId)
                .build());
        if (!resp.hasPreferenceSetting()) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "Company preference setting not found: " + companyId);
        }
        var timezone = resp.getPreferenceSetting().getTimeZone().getName();
        return getLocalDateTime(Instant.now(), timezone);
    }

    /*private*/ static LocalDateTime getLocalDateTime(Instant now, String timezone) {
        return now.atZone(ZoneId.of(timezone)).toLocalDateTime();
    }

    public CompanyPreferenceSettingModel getCompanyPreferenceSetting(long companyId) {
        var resp = companyStub.getCompanyPreferenceSetting(GetCompanyPreferenceSettingRequest.newBuilder()
                .setCompanyId(companyId)
                .build());
        return resp.getPreferenceSetting();
    }
}
