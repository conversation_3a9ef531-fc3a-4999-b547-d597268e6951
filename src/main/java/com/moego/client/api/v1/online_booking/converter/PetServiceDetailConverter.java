package com.moego.client.api.v1.online_booking.converter;

import com.moego.idl.client.online_booking.v1.PetServices;
import com.moego.idl.client.online_booking.v1.Service;
import com.moego.idl.models.online_booking.v1.BoardingServiceDetail;
import com.moego.idl.models.online_booking.v1.DaycareServiceDetail;
import com.moego.idl.models.online_booking.v1.DogWalkingServiceDetail;
import com.moego.idl.models.online_booking.v1.EvaluationServiceDetail;
import com.moego.idl.models.online_booking.v1.GroomingServiceDetail;
import com.moego.idl.models.online_booking.v1.GroupClassServiceDetail;
import com.moego.idl.models.online_booking.v1.Pet;
import com.moego.idl.models.online_booking.v1.PetServiceDetails;
import com.moego.idl.models.online_booking.v1.ServiceDetail;
import java.util.List;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface PetServiceDetailConverter {
    PetServiceDetailConverter INSTANCE = Mappers.getMapper(PetServiceDetailConverter.class);

    List<PetServiceDetails> toModels(List<PetServices> petServices);

    @Mapping(
            target = "isNewPet",
            expression = "java(!com.moego.common.utils.CommonUtil.isNormal(petServices.getPet().getPetId()))")
    @Mapping(target = "serviceDetails", source = "services")
    PetServiceDetails toModel(PetServices petServices);

    Pet toModel(com.moego.idl.client.online_booking.v1.Pet pet);

    @Mapping(
            target = "grooming",
            conditionExpression = "java(service.getServiceCase() == Service.ServiceCase.GROOMING)")
    @Mapping(
            target = "boarding",
            conditionExpression = "java(service.getServiceCase() == Service.ServiceCase.BOARDING)")
    @Mapping(target = "daycare", conditionExpression = "java(service.getServiceCase() == Service.ServiceCase.DAYCARE)")
    @Mapping(
            target = "evaluation",
            conditionExpression = "java(service.getServiceCase() == Service.ServiceCase.EVALUATION)")
    @Mapping(
            target = "dogWalking",
            conditionExpression = "java(service.getServiceCase() == Service.ServiceCase.DOG_WALKING)")
    @Mapping(
            target = "groupClass",
            conditionExpression = "java(service.getServiceCase() == Service.ServiceCase.GROUP_CLASS)")
    ServiceDetail toModel(Service service);

    GroomingServiceDetail toModel(Service.Grooming grooming);

    BoardingServiceDetail toModel(Service.Boarding boarding);

    DaycareServiceDetail toModel(Service.Daycare daycare);

    EvaluationServiceDetail toModel(Service.Evaluation evaluation);

    DogWalkingServiceDetail toModel(Service.DogWalking dogwalking);

    GroupClassServiceDetail toModel(Service.GroupClass groupClass);
}
