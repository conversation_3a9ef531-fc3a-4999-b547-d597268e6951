package com.moego.svc.online.booking.service;

import static com.moego.svc.online.booking.mapper.StaffAvailabilityDayHourDynamicSqlSupport.staffAvailabilityDayHour;
import static com.moego.svc.online.booking.mapper.StaffAvailabilitySlotDayDynamicSqlSupport.staffAvailabilitySlotDay;
import static com.moego.svc.online.booking.mapper.StaffAvailabilityTimeDayDynamicSqlSupport.staffAvailabilityTimeDay;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;
import static org.mybatis.dynamic.sql.SqlBuilder.isInWhenPresent;

import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.online_booking.v1.AvailabilityType;
import com.moego.idl.models.organization.v1.SlotAvailabilityDay;
import com.moego.idl.models.organization.v1.SlotAvailabilityDayDef;
import com.moego.idl.models.organization.v1.SlotDailySetting;
import com.moego.idl.models.organization.v1.SlotHourSetting;
import com.moego.idl.models.organization.v1.SlotHourSettingDef;
import com.moego.idl.models.organization.v1.StaffAvailabilityDef;
import com.moego.idl.models.organization.v1.TimeAvailabilityDay;
import com.moego.idl.models.organization.v1.TimeAvailabilityDayDef;
import com.moego.idl.models.organization.v1.TimeDailySetting;
import com.moego.idl.models.organization.v1.TimeHourSetting;
import com.moego.idl.models.organization.v1.TimeHourSettingDef;
import com.moego.svc.online.booking.dto.StaffAvailabilityDayDTO;
import com.moego.svc.online.booking.entity.DayHourLimit;
import com.moego.svc.online.booking.entity.DayHourLimitGroup;
import com.moego.svc.online.booking.entity.StaffAvailabilityDayHour;
import com.moego.svc.online.booking.entity.StaffAvailabilitySlotDay;
import com.moego.svc.online.booking.entity.StaffAvailabilityTimeDay;
import com.moego.svc.online.booking.mapper.StaffAvailabilityDayHourMapper;
import com.moego.svc.online.booking.mapper.StaffAvailabilitySlotDayMapper;
import com.moego.svc.online.booking.mapper.StaffAvailabilityTimeDayMapper;
import com.moego.svc.online.booking.utils.OBAvailabilityDayHourUtils;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class StaffAvailabilityDayHourService {
    private final StaffAvailabilitySlotDayMapper staffAvailabilitySlotDayMapper;
    private final StaffAvailabilityTimeDayMapper staffAvailabilityTimeDayMapper;
    private final StaffAvailabilityDayHourMapper staffAvailabilityDayHourMapper;
    private final BookingLimitationService bookingLimitationService;

    public List<StaffAvailabilitySlotDay> getStaffAvailabilitySlotDays(Long businessId, List<Long> staffIdList) {
        return staffAvailabilitySlotDayMapper.select(
                c -> c.where(staffAvailabilitySlotDay.businessId, isEqualTo(businessId))
                        .and(staffAvailabilitySlotDay.staffId, isInWhenPresent(staffIdList))
                        .orderBy(staffAvailabilitySlotDay.dayOfWeek));
    }

    public List<StaffAvailabilityTimeDay> getStaffAvailabilityTimeDays(Long businessId, List<Long> staffIdList) {
        return staffAvailabilityTimeDayMapper.select(
                c -> c.where(staffAvailabilityTimeDay.businessId, isEqualTo(businessId))
                        .and(staffAvailabilityTimeDay.staffId, isInWhenPresent(staffIdList))
                        .orderBy(staffAvailabilityTimeDay.dayOfWeek));
    }

    public Map<Long, List<SlotAvailabilityDay>> getStaffAvailabilitySlotDayDetails(
            Long businessId, List<Long> staffIdList) {

        List<StaffAvailabilitySlotDay> staffAvailabilitySlotDays =
                getStaffAvailabilitySlotDays(businessId, staffIdList);

        List<Long> limitIds = new ArrayList<>();
        List<Long> dayIds = new ArrayList<>();
        staffAvailabilitySlotDays.forEach(staffAvailabilitySlotDay -> {
            limitIds.addAll(staffAvailabilitySlotDay.getLimitIds());
            dayIds.add(staffAvailabilitySlotDay.getId());
        });

        // dayId -> StaffAvailabilityDayHourList
        Map<Long, List<StaffAvailabilityDayHour>> staffAvailabilityDayHourMap =
                getStaffAvailabilityDayHourMapByDayIds(dayIds, AvailabilityType.AVAILABILITY_TYPE_BY_SLOTS);

        staffAvailabilityDayHourMap.forEach(
                (dayId, staffAvailabilityDayHourList) -> staffAvailabilityDayHourList.forEach(
                        staffAvailabilityDayHour -> limitIds.addAll(staffAvailabilityDayHour.getLimitIds())));

        // limitId -> DayHourLimit
        Map<Long, DayHourLimit> dayHourLimitMap = bookingLimitationService.getDayHourLimitMapByLimitIds(limitIds);
        var groupIds = dayHourLimitMap.values().stream()
                .map(DayHourLimit::getGroupId)
                .filter(CommonUtil::isNormal)
                .distinct()
                .toList();
        Map<Long, DayHourLimitGroup> dayHourLimitGroupMap = bookingLimitationService.getDayHourLimitGroupMap(groupIds);

        return staffAvailabilitySlotDays.stream()
                .collect(Collectors.groupingBy(
                        StaffAvailabilitySlotDay::getStaffId,
                        Collectors.mapping(
                                staffAvailabilitySlotDay -> {
                                    // 获取slotDayLimits
                                    List<DayHourLimit> slotDayLimits = staffAvailabilitySlotDay.getLimitIds().stream()
                                            .map(dayHourLimitMap::get)
                                            .toList();

                                    // 获取day hour & build slot hour setting
                                    var dayHour = staffAvailabilityDayHourMap.get(staffAvailabilitySlotDay.getId());
                                    List<SlotHourSetting> slotHourSettings;
                                    if (CollectionUtils.isEmpty(dayHour)) {
                                        slotHourSettings = List.of();
                                    } else {
                                        slotHourSettings = dayHour.stream()
                                                .map(staffAvailabilityDayHour -> {
                                                    List<DayHourLimit> dayHourLimits =
                                                            staffAvailabilityDayHour.getLimitIds().stream()
                                                                    .map(dayHourLimitMap::get)
                                                                    .toList();
                                                    return SlotHourSetting.newBuilder()
                                                            .setStartTime(staffAvailabilityDayHour.getStartTime())
                                                            .setCapacity(staffAvailabilityDayHour.getCapacity())
                                                            .addAllLimitationGroups(
                                                                    OBAvailabilityDayHourUtils
                                                                            .buildBookingLimitationPBModel(
                                                                                    dayHourLimits,
                                                                                    dayHourLimitGroupMap))
                                                            .build();
                                                })
                                                .toList();
                                    }

                                    // build slot day
                                    return SlotAvailabilityDay.newBuilder()
                                            .setDayOfWeekValue(staffAvailabilitySlotDay.getDayOfWeek())
                                            .setIsAvailable(staffAvailabilitySlotDay.getIsAvailable())
                                            .setSlotDailySetting(SlotDailySetting.newBuilder()
                                                    .setStartTime(staffAvailabilitySlotDay.getStartTime())
                                                    .setEndTime(staffAvailabilitySlotDay.getEndTime())
                                                    .setCapacity(staffAvailabilitySlotDay.getCapacity())
                                                    .addAllLimitationGroups(
                                                            OBAvailabilityDayHourUtils.buildBookingLimitationPBModel(
                                                                    slotDayLimits, dayHourLimitGroupMap))
                                                    .build())
                                            .addAllSlotHourSettingList(slotHourSettings)
                                            .build();
                                },
                                Collectors.toList())));
    }

    public Map<Long, List<TimeAvailabilityDay>> getStaffAvailabilityTimeDayDetails(
            Long businessId, List<Long> staffIdList) {
        List<StaffAvailabilityTimeDay> staffAvailabilityTimeDays =
                getStaffAvailabilityTimeDays(businessId, staffIdList);

        List<Long> limitIds = new ArrayList<>();
        List<Long> dayIds = new ArrayList<>();
        staffAvailabilityTimeDays.forEach(staffAvailabilityTimeDay -> {
            limitIds.addAll(staffAvailabilityTimeDay.getLimitIds());
            dayIds.add(staffAvailabilityTimeDay.getId());
        });

        // dayId -> StaffAvailabilityDayHourList
        Map<Long, List<StaffAvailabilityDayHour>> staffAvailabilityDayHourMap =
                getStaffAvailabilityDayHourMapByDayIds(dayIds, AvailabilityType.AVAILABILITY_TYPE_BY_WORKING_HOURS);

        staffAvailabilityDayHourMap.forEach(
                (dayId, staffAvailabilityDayHourList) -> staffAvailabilityDayHourList.forEach(
                        staffAvailabilityDayHour -> limitIds.addAll(staffAvailabilityDayHour.getLimitIds())));

        // limitId -> DayHourLimit
        Map<Long, DayHourLimit> dayHourLimitMap = bookingLimitationService.getDayHourLimitMapByLimitIds(limitIds);
        var groupIds = dayHourLimitMap.values().stream()
                .map(DayHourLimit::getGroupId)
                .filter(CommonUtil::isNormal)
                .distinct()
                .toList();
        Map<Long, DayHourLimitGroup> dayHourLimitGroupMap = bookingLimitationService.getDayHourLimitGroupMap(groupIds);

        return staffAvailabilityTimeDays.stream()
                .collect(Collectors.groupingBy(
                        StaffAvailabilityTimeDay::getStaffId,
                        Collectors.mapping(
                                staffAvailabilityTimeDay -> {
                                    // 获取timeDayLimits
                                    List<DayHourLimit> timeDayLimits = staffAvailabilityTimeDay.getLimitIds().stream()
                                            .map(dayHourLimitMap::get)
                                            .toList();

                                    // 获取day hour & build time hour setting
                                    var dayHour = staffAvailabilityDayHourMap.get(staffAvailabilityTimeDay.getId());
                                    List<TimeHourSetting> timeHourSettings;
                                    if (CollectionUtils.isEmpty(dayHour)) {
                                        timeHourSettings = List.of();
                                    } else {
                                        timeHourSettings = dayHour.stream()
                                                .map(staffAvailabilityDayHour -> TimeHourSetting.newBuilder()
                                                        .setStartTime(staffAvailabilityDayHour.getStartTime())
                                                        .setEndTime(staffAvailabilityDayHour.getEndTime())
                                                        .build())
                                                .toList();
                                    }

                                    // build time day
                                    return TimeAvailabilityDay.newBuilder()
                                            .setDayOfWeekValue(staffAvailabilityTimeDay.getDayOfWeek())
                                            .setIsAvailable(staffAvailabilityTimeDay.getIsAvailable())
                                            .setTimeDailySetting(TimeDailySetting.newBuilder()
                                                    .addAllLimitationGroups(
                                                            OBAvailabilityDayHourUtils.buildBookingLimitationPBModel(
                                                                    timeDayLimits, dayHourLimitGroupMap))
                                                    .build())
                                            .addAllTimeHourSettingList(timeHourSettings)
                                            .build();
                                },
                                Collectors.toList())));
    }

    @Transactional
    public void updateStaffAvailabilitySlotDays(
            Long companyId,
            Long businessId,
            StaffAvailabilityDef staffAvailabilityDef,
            Map<String, StaffAvailabilitySlotDay> availabilitySlotDayMap) {

        Set<Long> deleteLimitIds = new HashSet<>();
        Set<Long> deleteLimitGroupIds = new HashSet<>();
        staffAvailabilityDef.getSlotAvailabilityDayListList().forEach(slotAvailabilityDayDef -> {
            StaffAvailabilitySlotDay staffAvailabilitySlotDay =
                    availabilitySlotDayMap.get(OBAvailabilityDayHourUtils.getStaffAvailabilityDayKey(
                            businessId, staffAvailabilityDef.getStaffId(), slotAvailabilityDayDef.getDayOfWeekValue()));

            StaffAvailabilityDayDTO staffAvailabilityDayDTO = new StaffAvailabilityDayDTO();
            staffAvailabilityDayDTO.setCompanyId(companyId);
            staffAvailabilityDayDTO.setBusinessId(businessId);
            staffAvailabilityDayDTO.setStaffId(staffAvailabilityDef.getStaffId());
            staffAvailabilityDayDTO.setStaffAvailabilitySlotDay(staffAvailabilitySlotDay);

            // update staff availability slot day, and write deleteLimitIds
            updateStaffAvailabilitySlotDay(staffAvailabilityDayDTO, slotAvailabilityDayDef, deleteLimitIds);
        });

        // 删除limit
        if (!CollectionUtils.isEmpty(deleteLimitIds)) {
            bookingLimitationService.deleteByIds(deleteLimitIds.stream().toList());
        }
        if (!CollectionUtils.isEmpty(deleteLimitGroupIds)) {
            bookingLimitationService.deleteGroupByIds(
                    deleteLimitGroupIds.stream().toList());
        }
    }

    @Transactional
    public void updateStaffAvailabilityTimeDays(
            Long companyId,
            Long businessId,
            StaffAvailabilityDef staffAvailabilityDef,
            Map<String, StaffAvailabilityTimeDay> availabilityTimeDayMap) {

        Set<Long> deleteLimitIds = new HashSet<>();
        staffAvailabilityDef.getTimeAvailabilityDayListList().forEach(timeAvailabilityDayDef -> {
            StaffAvailabilityTimeDay staffAvailabilityTimeDay =
                    availabilityTimeDayMap.get(OBAvailabilityDayHourUtils.getStaffAvailabilityDayKey(
                            businessId, staffAvailabilityDef.getStaffId(), timeAvailabilityDayDef.getDayOfWeekValue()));

            StaffAvailabilityDayDTO staffAvailabilityDayDTO = new StaffAvailabilityDayDTO();
            staffAvailabilityDayDTO.setCompanyId(companyId);
            staffAvailabilityDayDTO.setBusinessId(businessId);
            staffAvailabilityDayDTO.setStaffId(staffAvailabilityDef.getStaffId());
            staffAvailabilityDayDTO.setStaffAvailabilityTimeDay(staffAvailabilityTimeDay);

            // update staff availability slot day, and write deleteLimitIds
            updateStaffAvailabilityTimeDay(staffAvailabilityDayDTO, timeAvailabilityDayDef, deleteLimitIds);
        });

        // 删除limit
        if (!CollectionUtils.isEmpty(deleteLimitIds)) {
            bookingLimitationService.deleteByIds(deleteLimitIds.stream().toList());
        }
    }

    public void updateStaffAvailabilitySlotDay(
            StaffAvailabilityDayDTO staffAvailabilityDayDTO,
            SlotAvailabilityDayDef slotAvailabilityDayDef,
            Set<Long> deleteLimitIds) {

        Long dayId;
        List<StaffAvailabilityDayHour> staffDayHours = new ArrayList<>();
        StaffAvailabilitySlotDay staffAvailabilitySlotDay = staffAvailabilityDayDTO.getStaffAvailabilitySlotDay();

        if (staffAvailabilitySlotDay != null) {
            // data exists, update
            dayId = staffAvailabilitySlotDay.getId();
            deleteLimitIds.addAll(staffAvailabilitySlotDay.getLimitIds());
            // List<DayHourLimit> dayHourLimits = OBAvailabilityDayHourUtils.buildDayHourLimits(
            //         slotAvailabilityDayDef.getSlotDailySetting().getLimit());
            // // create limit
            // bookingLimitationService.batchCreate(dayHourLimits);
            // var newLimitIds = dayHourLimits.stream().map(DayHourLimit::getId).toList();

            var newLimitIds = bookingLimitationService.batchCreateByLimitationGroup(
                    slotAvailabilityDayDef.getSlotDailySetting().getLimitationGroupsList());

            staffAvailabilitySlotDay.setIsAvailable(slotAvailabilityDayDef.getIsAvailable());
            staffAvailabilitySlotDay.setStartTime(
                    slotAvailabilityDayDef.getSlotDailySetting().getStartTime());
            staffAvailabilitySlotDay.setEndTime(
                    slotAvailabilityDayDef.getSlotDailySetting().getEndTime());
            staffAvailabilitySlotDay.setCapacity(
                    slotAvailabilityDayDef.getSlotDailySetting().getCapacity());
            staffAvailabilitySlotDay.setLimitIds(newLimitIds);
            staffAvailabilitySlotDay.setUpdatedAt(new Date());
            updateSlotDay(staffAvailabilitySlotDay);
        } else {
            // List<DayHourLimit> dayHourLimits = OBAvailabilityDayHourUtils.buildDayHourLimits(
            //         slotAvailabilityDayDef.getSlotDailySetting().getLimit());
            // // create limit
            // bookingLimitationService.batchCreate(dayHourLimits);
            // var newLimitIds = dayHourLimits.stream().map(DayHourLimit::getId).toList();

            var newLimitIds = bookingLimitationService.batchCreateByLimitationGroup(
                    slotAvailabilityDayDef.getSlotDailySetting().getLimitationGroupsList());

            var slotDayEntity = OBAvailabilityDayHourUtils.buildStaffSlotDay(
                    staffAvailabilityDayDTO.getCompanyId(),
                    staffAvailabilityDayDTO.getBusinessId(),
                    staffAvailabilityDayDTO.getStaffId(),
                    slotAvailabilityDayDef,
                    newLimitIds);

            createStaffAvailabilitySlotDay(slotDayEntity);

            dayId = slotDayEntity.getId();
        }

        slotAvailabilityDayDef.getSlotHourSettingListList().forEach(slotHourSetting -> {
            // List<DayHourLimit> dayHourLimits =
            //         OBAvailabilityDayHourUtils.buildDayHourLimits(slotHourSetting.getLimit());
            // // create limit
            // bookingLimitationService.batchCreate(dayHourLimits);
            // var newLimitIds = dayHourLimits.stream().map(DayHourLimit::getId).toList();

            var newLimitIds =
                    bookingLimitationService.batchCreateByLimitationGroup(slotHourSetting.getLimitationGroupsList());

            var staffSlotDayHour = getStaffAvailabilitySlotDayHour(slotHourSetting, dayId, newLimitIds);

            staffDayHours.add(staffSlotDayHour);
        });
        createStaffAvailabilityDayHour(dayId, AvailabilityType.AVAILABILITY_TYPE_BY_SLOTS, staffDayHours);
    }

    public void updateStaffAvailabilityTimeDay(
            StaffAvailabilityDayDTO staffAvailabilityDayDTO,
            TimeAvailabilityDayDef timeAvailabilityDayDef,
            Set<Long> deleteLimitIds) {

        Long dayId;
        List<StaffAvailabilityDayHour> staffDayHours = new ArrayList<>();
        StaffAvailabilityTimeDay staffAvailabilityTimeDay = staffAvailabilityDayDTO.getStaffAvailabilityTimeDay();

        if (staffAvailabilityTimeDay != null) {
            // data exists, update
            dayId = staffAvailabilityTimeDay.getId();
            deleteLimitIds.addAll(staffAvailabilityTimeDay.getLimitIds());
            // List<DayHourLimit> dayHourLimits = OBAvailabilityDayHourUtils.buildDayHourLimits(
            //         timeAvailabilityDayDef.getTimeDailySetting().getLimit());
            // // create limit
            // bookingLimitationService.batchCreate(dayHourLimits);
            // var newLimitIds = dayHourLimits.stream().map(DayHourLimit::getId).toList();

            var newLimitIds = bookingLimitationService.batchCreateByLimitationGroup(
                    timeAvailabilityDayDef.getTimeDailySetting().getLimitationGroupsList());

            staffAvailabilityTimeDay.setIsAvailable(timeAvailabilityDayDef.getIsAvailable());
            staffAvailabilityTimeDay.setLimitIds(newLimitIds);
            staffAvailabilityTimeDay.setUpdatedAt(new Date());
            updateTimeDay(staffAvailabilityTimeDay);
        } else {
            // List<DayHourLimit> dayHourLimits = OBAvailabilityDayHourUtils.buildDayHourLimits(
            //         timeAvailabilityDayDef.getTimeDailySetting().getLimit());
            // // create limit
            // bookingLimitationService.batchCreate(dayHourLimits);
            // var newLimitIds = dayHourLimits.stream().map(DayHourLimit::getId).toList();

            var newLimitIds = bookingLimitationService.batchCreateByLimitationGroup(
                    timeAvailabilityDayDef.getTimeDailySetting().getLimitationGroupsList());

            var timeDayEntity = OBAvailabilityDayHourUtils.buildStaffTimeDay(
                    staffAvailabilityDayDTO.getCompanyId(),
                    staffAvailabilityDayDTO.getBusinessId(),
                    staffAvailabilityDayDTO.getStaffId(),
                    timeAvailabilityDayDef,
                    newLimitIds);

            staffAvailabilityTimeDayMapper.insertSelective(timeDayEntity);

            dayId = timeDayEntity.getId();
        }

        timeAvailabilityDayDef.getTimeHourSettingListList().forEach(timeHourSetting -> {
            var staffTimeDayHour = getStaffAvailabilityTimeDayHour(timeHourSetting, dayId);
            staffDayHours.add(staffTimeDayHour);
        });
        createStaffAvailabilityDayHour(dayId, AvailabilityType.AVAILABILITY_TYPE_BY_WORKING_HOURS, staffDayHours);
    }

    public static StaffAvailabilityDayHour getStaffAvailabilitySlotDayHour(
            SlotHourSettingDef slotHourSetting, Long dayId, List<Long> newLimitIds) {
        var staffSlotDayHour = new StaffAvailabilityDayHour();
        staffSlotDayHour.setDayId(dayId);
        staffSlotDayHour.setDayType(AvailabilityType.AVAILABILITY_TYPE_BY_SLOTS.getNumber());
        staffSlotDayHour.setStartTime(slotHourSetting.getStartTime());
        staffSlotDayHour.setEndTime(0); // set default
        staffSlotDayHour.setCapacity(slotHourSetting.getCapacity());
        staffSlotDayHour.setLimitIds(newLimitIds);
        staffSlotDayHour.setCreatedAt(new Date());
        staffSlotDayHour.setUpdatedAt(new Date());
        return staffSlotDayHour;
    }

    public static StaffAvailabilityDayHour getStaffAvailabilityTimeDayHour(
            TimeHourSettingDef timeHourSetting, Long dayId) {
        var staffTimeDayHour = new StaffAvailabilityDayHour();
        staffTimeDayHour.setDayId(dayId);
        staffTimeDayHour.setDayType(AvailabilityType.AVAILABILITY_TYPE_BY_WORKING_HOURS.getNumber());
        staffTimeDayHour.setStartTime(timeHourSetting.getStartTime());
        staffTimeDayHour.setEndTime(timeHourSetting.getEndTime());
        staffTimeDayHour.setCapacity(0); // set default
        staffTimeDayHour.setLimitIds(List.of()); // set default
        staffTimeDayHour.setCreatedAt(new Date());
        staffTimeDayHour.setUpdatedAt(new Date());
        return staffTimeDayHour;
    }

    public void createStaffAvailabilityDayHour(
            Long dayId, AvailabilityType dayType, List<StaffAvailabilityDayHour> staffAvailabilityDayHourEntityList) {
        // 1. 获取该dayId dayType所有的limitIds
        Set<Long> deleteLimitIds = new HashSet<>();
        List<StaffAvailabilityDayHour> staffAvailabilityDayHours =
                staffAvailabilityDayHourMapper.select(c -> c.where(staffAvailabilityDayHour.dayId, isEqualTo(dayId))
                        .and(staffAvailabilityDayHour.dayType, isEqualTo(dayType.getNumber())));
        staffAvailabilityDayHours.forEach(staffAvailabilityDayHour -> {
            deleteLimitIds.addAll(staffAvailabilityDayHour.getLimitIds());
        });

        // 2. 删除dayId dayType所有的StaffAvailabilityDayHour
        staffAvailabilityDayHourMapper.delete(c -> c.where(staffAvailabilityDayHour.dayId, isEqualTo(dayId))
                .and(staffAvailabilityDayHour.dayType, isEqualTo(dayType.getNumber())));

        // 3. create
        if (!CollectionUtils.isEmpty(staffAvailabilityDayHourEntityList)) {
            staffAvailabilityDayHourEntityList.forEach(staffAvailabilityDayHourMapper::insertSelective);
        }

        // 4. 删除limit
        if (!CollectionUtils.isEmpty(deleteLimitIds)) {
            bookingLimitationService.deleteByIds(deleteLimitIds.stream().toList());
        }
    }

    public void createStaffAvailabilitySlotDay(StaffAvailabilitySlotDay staffAvailabilitySlotDay) {
        staffAvailabilitySlotDayMapper.insertSelective(staffAvailabilitySlotDay);
    }

    public void batchCreateStaffAvailabilitySlotDay(List<StaffAvailabilitySlotDay> staffAvailabilitySlotDays) {
        if (CollectionUtils.isEmpty(staffAvailabilitySlotDays)) {
            return;
        }
        staffAvailabilitySlotDays.forEach(staffAvailabilitySlotDayMapper::insertSelective);
    }

    public void batchCreateStaffAvailabilityTimeDay(List<StaffAvailabilityTimeDay> staffAvailabilityTimeDays) {
        if (CollectionUtils.isEmpty(staffAvailabilityTimeDays)) {
            return;
        }
        staffAvailabilityTimeDays.forEach(staffAvailabilityTimeDayMapper::insertSelective);
    }

    public void batchCreateStaffAvailabilityDayHour(List<StaffAvailabilityDayHour> staffAvailabilityDayHours) {
        if (CollectionUtils.isEmpty(staffAvailabilityDayHours)) {
            return;
        }
        staffAvailabilityDayHours.forEach(staffAvailabilityDayHourMapper::insertSelective);
    }

    public Map<String, StaffAvailabilitySlotDay> getAvailabilitySlotDayMapByStaffIds(
            Long businessId, List<Long> staffIdList) {
        List<StaffAvailabilitySlotDay> staffAvailabilitySlotDays = staffAvailabilitySlotDayMapper.select(
                c -> c.where(staffAvailabilitySlotDay.businessId, isEqualTo(businessId))
                        .and(staffAvailabilitySlotDay.staffId, isIn(staffIdList)));

        return staffAvailabilitySlotDays.stream()
                .collect(Collectors.toMap(
                        staffAvailabilitySlotDay -> OBAvailabilityDayHourUtils.getStaffAvailabilityDayKey(
                                staffAvailabilitySlotDay.getBusinessId(),
                                staffAvailabilitySlotDay.getStaffId(),
                                staffAvailabilitySlotDay.getDayOfWeek()),
                        staffAvailabilitySlotDay -> staffAvailabilitySlotDay));
    }

    public Map<String, StaffAvailabilityTimeDay> getAvailabilityTimeDayMapByStaffIds(
            Long businessId, List<Long> staffIdList) {
        List<StaffAvailabilityTimeDay> staffAvailabilityTimeDays = staffAvailabilityTimeDayMapper.select(
                c -> c.where(staffAvailabilityTimeDay.businessId, isEqualTo(businessId))
                        .and(staffAvailabilityTimeDay.staffId, isIn(staffIdList)));

        return staffAvailabilityTimeDays.stream()
                .collect(Collectors.toMap(
                        staffAvailabilityTimeDay -> OBAvailabilityDayHourUtils.getStaffAvailabilityDayKey(
                                staffAvailabilityTimeDay.getBusinessId(),
                                staffAvailabilityTimeDay.getStaffId(),
                                staffAvailabilityTimeDay.getDayOfWeek()),
                        staffAvailabilityTimeDay -> staffAvailabilityTimeDay));
    }

    public Map<Long, List<StaffAvailabilityDayHour>> getStaffAvailabilityDayHourMapByDayIds(
            List<Long> dayIds, AvailabilityType dayType) {
        if (CollectionUtils.isEmpty(dayIds)) {
            return Map.of();
        }
        List<StaffAvailabilityDayHour> staffAvailabilityDayHours =
                staffAvailabilityDayHourMapper.select(c -> c.where(staffAvailabilityDayHour.dayId, isIn(dayIds))
                        .and(staffAvailabilityDayHour.dayType, isEqualTo(dayType.getNumber()))
                        .orderBy(staffAvailabilityDayHour.startTime));

        return staffAvailabilityDayHours.stream().collect(Collectors.groupingBy(StaffAvailabilityDayHour::getDayId));
    }

    public void updateSlotDay(StaffAvailabilitySlotDay staffSlotDay) {

        staffAvailabilitySlotDayMapper.update(c -> c.set(staffAvailabilitySlotDay.isAvailable)
                .equalTo(staffSlotDay.getIsAvailable())
                .set(staffAvailabilitySlotDay.capacity)
                .equalTo(staffSlotDay.getCapacity())
                .set(staffAvailabilitySlotDay.startTime)
                .equalTo(staffSlotDay.getStartTime())
                .set(staffAvailabilitySlotDay.endTime)
                .equalTo(staffSlotDay.getEndTime())
                .set(staffAvailabilitySlotDay.limitIds)
                .equalTo(staffSlotDay.getLimitIds())
                .set(staffAvailabilitySlotDay.updatedAt)
                .equalTo(staffSlotDay.getUpdatedAt())
                .where(staffAvailabilitySlotDay.companyId, isEqualTo(staffSlotDay::getCompanyId))
                .and(staffAvailabilitySlotDay.businessId, isEqualTo(staffSlotDay::getBusinessId))
                .and(staffAvailabilitySlotDay.staffId, isEqualTo(staffSlotDay::getStaffId))
                .and(staffAvailabilitySlotDay.dayOfWeek, isEqualTo(staffSlotDay::getDayOfWeek)));
    }

    public void updateTimeDay(StaffAvailabilityTimeDay staffTimeDay) {

        staffAvailabilityTimeDayMapper.update(c -> c.set(staffAvailabilityTimeDay.isAvailable)
                .equalTo(staffTimeDay.getIsAvailable())
                .set(staffAvailabilityTimeDay.limitIds)
                .equalTo(staffTimeDay.getLimitIds())
                .set(staffAvailabilityTimeDay.updatedAt)
                .equalTo(staffTimeDay.getUpdatedAt())
                .where(staffAvailabilityTimeDay.companyId, isEqualTo(staffTimeDay::getCompanyId))
                .and(staffAvailabilityTimeDay.businessId, isEqualTo(staffTimeDay::getBusinessId))
                .and(staffAvailabilityTimeDay.staffId, isEqualTo(staffTimeDay::getStaffId))
                .and(staffAvailabilityTimeDay.dayOfWeek, isEqualTo(staffTimeDay::getDayOfWeek)));
    }
}
