package com.moego.svc.organization.mapper.base;

import com.moego.svc.organization.entity.DayHourLimitGroup;
import com.moego.svc.organization.entity.DayHourLimitGroupExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseDayHourLimitGroupMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table day_hour_limit_group
     *
     * @mbg.generated
     */
    long countByExample(DayHourLimitGroupExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table day_hour_limit_group
     *
     * @mbg.generated
     */
    int deleteByExample(DayHourLimitGroupExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table day_hour_limit_group
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table day_hour_limit_group
     *
     * @mbg.generated
     */
    int insert(DayHourLimitGroup row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table day_hour_limit_group
     *
     * @mbg.generated
     */
    int insertSelective(DayHourLimitGroup row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table day_hour_limit_group
     *
     * @mbg.generated
     */
    List<DayHourLimitGroup> selectByExample(DayHourLimitGroupExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table day_hour_limit_group
     *
     * @mbg.generated
     */
    DayHourLimitGroup selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table day_hour_limit_group
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("row") DayHourLimitGroup row, @Param("example") DayHourLimitGroupExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table day_hour_limit_group
     *
     * @mbg.generated
     */
    int updateByExample(@Param("row") DayHourLimitGroup row, @Param("example") DayHourLimitGroupExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table day_hour_limit_group
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(DayHourLimitGroup row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table day_hour_limit_group
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(DayHourLimitGroup row);
}
