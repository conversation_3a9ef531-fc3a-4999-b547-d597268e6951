package com.moego.svc.organization.entity;

import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor(staticName = "")
@NoArgsConstructor(staticName = "")
public class DayHourLimit {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column day_hour_limit.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column day_hour_limit.type
     *
     * @mbg.generated
     */
    private Integer type;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column day_hour_limit.pet_type_id
     *
     * @mbg.generated
     */
    private Long petTypeId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column day_hour_limit.is_all_breed
     *
     * @mbg.generated
     */
    private Boolean isAllBreed;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column day_hour_limit.is_all_service
     *
     * @mbg.generated
     */
    private Boolean isAllService;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column day_hour_limit.capacity
     *
     * @mbg.generated
     */
    private Integer capacity;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column day_hour_limit.created_at
     *
     * @mbg.generated
     */
    private LocalDateTime createdAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column day_hour_limit.updated_at
     *
     * @mbg.generated
     */
    private LocalDateTime updatedAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column day_hour_limit.group_id
     *
     * @mbg.generated
     */
    private Long groupId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column day_hour_limit.pet_size_ids
     *
     * @mbg.generated
     */
    private List<Long> petSizeIds;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column day_hour_limit.breed_ids
     *
     * @mbg.generated
     */
    private List<Long> breedIds;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column day_hour_limit.service_ids
     *
     * @mbg.generated
     */
    private List<Long> serviceIds;
}
