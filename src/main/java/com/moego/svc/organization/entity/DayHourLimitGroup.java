package com.moego.svc.organization.entity;

import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor(staticName = "")
@NoArgsConstructor(staticName = "")
public class DayHourLimitGroup {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column day_hour_limit_group.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column day_hour_limit_group.only_accept_selected
     *
     * @mbg.generated
     */
    private Boolean onlyAcceptSelected;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column day_hour_limit_group.created_at
     *
     * @mbg.generated
     */
    private LocalDateTime createdAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column day_hour_limit_group.updated_at
     *
     * @mbg.generated
     */
    private LocalDateTime updatedAt;
}
