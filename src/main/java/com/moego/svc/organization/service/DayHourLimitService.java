package com.moego.svc.organization.service;

import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.organization.v1.LimitationGroup;
import com.moego.idl.models.organization.v1.LimitationGroupDef;
import com.moego.svc.organization.entity.DayHourLimit;
import com.moego.svc.organization.entity.DayHourLimitExample;
import com.moego.svc.organization.entity.DayHourLimitGroup;
import com.moego.svc.organization.entity.DayHourLimitGroupExample;
import com.moego.svc.organization.mapper.base.BaseDayHourLimit;
import com.moego.svc.organization.mapper.base.BaseDayHourLimitGroupMapper;
import com.moego.svc.organization.utils.AvailabilityDayHourUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class DayHourLimitService {
    private final BaseDayHourLimit dayHourLimitMapper;
    private final BaseDayHourLimitGroupMapper dayHourLimitGroupMapper;

    public Map<Long, DayHourLimit> getDayHourLimitMapByLimitIds(List<Long> limitIds) {
        if (limitIds.isEmpty()) {
            return Map.of();
        }
        DayHourLimitExample example = new DayHourLimitExample();
        var criteria = example.createCriteria();
        criteria.andIdIn(limitIds);
        return dayHourLimitMapper.selectByExampleWithBLOBs(example).stream()
                .collect(Collectors.toMap(DayHourLimit::getId, Function.identity()));
    }

    // 删除limit ids
    public void deleteByIds(List<Long> limitIds) {
        if (limitIds.isEmpty()) {
            return;
        }
        DayHourLimitExample example = new DayHourLimitExample();
        var criteria = example.createCriteria();
        criteria.andIdIn(limitIds);
        var groupIds = dayHourLimitMapper.selectByExampleWithBLOBs(example).stream()
                .map(DayHourLimit::getGroupId)
                .filter(CommonUtil::isNormal)
                .distinct()
                .toList();
        if (!CollectionUtils.isEmpty(groupIds)) {
            deleteGroupByIds(groupIds);
        }

        dayHourLimitMapper.deleteByExample(example);
    }

    // 创建多个limit
    public void createDayHourLimits(List<DayHourLimit> dayHourLimits) {
        if (dayHourLimits.isEmpty()) {
            return;
        }
        for (var dayHourLimit : dayHourLimits) {
            dayHourLimitMapper.insertSelective(dayHourLimit);
        }
    }

    public Map<Long, DayHourLimitGroup> getDayHourLimitGroupMap(List<Long> groupIds) {
        if (groupIds.isEmpty()) {
            return Map.of();
        }
        DayHourLimitGroupExample example = new DayHourLimitGroupExample();
        var criteria = example.createCriteria();
        criteria.andIdIn(groupIds);
        return dayHourLimitGroupMapper.selectByExample(example).stream()
                .collect(Collectors.toMap(DayHourLimitGroup::getId, Function.identity()));
    }

    public void deleteGroupByIds(List<Long> groupIds) {
        if (groupIds.isEmpty()) {
            return;
        }
        DayHourLimitGroupExample example = new DayHourLimitGroupExample();
        var criteria = example.createCriteria();
        criteria.andIdIn(groupIds);
        dayHourLimitGroupMapper.deleteByExample(example);
    }

    public List<Long> batchCreateByLimitation(List<LimitationGroup> limitationGroups) {
        if (CollectionUtils.isEmpty(limitationGroups)) {
            return List.of();
        }
        List<Long> limitIds = new ArrayList<>();
        limitationGroups.forEach(limitationGroup -> {
            var dayHourLimitGroup = new DayHourLimitGroup();
            dayHourLimitGroup.setOnlyAcceptSelected(limitationGroup.getOnlyAcceptSelected());
            dayHourLimitGroupMapper.insertSelective(dayHourLimitGroup);

            var dayHourLimits = AvailabilityDayHourUtils.buildDayHourLimits(limitationGroup, dayHourLimitGroup.getId());
            createDayHourLimits(dayHourLimits);
            dayHourLimits.stream().map(DayHourLimit::getId).forEach(limitIds::add);
        });
        return limitIds;
    }

    public List<Long> batchCreateByLimitationDef(List<LimitationGroupDef> limitationGroups) {
        if (CollectionUtils.isEmpty(limitationGroups)) {
            return List.of();
        }
        List<Long> limitIds = new ArrayList<>();
        limitationGroups.forEach(limitationGroup -> {
            var dayHourLimitGroup = new DayHourLimitGroup();
            dayHourLimitGroup.setOnlyAcceptSelected(limitationGroup.getOnlyAcceptSelected());
            dayHourLimitGroupMapper.insertSelective(dayHourLimitGroup);

            var dayHourLimits = AvailabilityDayHourUtils.buildDayHourLimits(limitationGroup, dayHourLimitGroup.getId());
            createDayHourLimits(dayHourLimits);
            dayHourLimits.stream().map(DayHourLimit::getId).forEach(limitIds::add);
        });
        return limitIds;
    }
}
