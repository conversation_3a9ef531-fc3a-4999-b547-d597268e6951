package com.moego.svc.appointment.controller;

import static com.moego.lib.common.exception.ExceptionUtil.bizException;
import static com.moego.svc.appointment.constant.AppointmentStatusSet.ACTIVE_STATUS_SET;
import static com.moego.svc.appointment.constant.AppointmentStatusSet.EXPECTED_STATUS_SET;
import static com.moego.svc.appointment.utils.LineItemUtils.PET_DETAIL_EXTERNAL_ID_PREFIX;
import static java.util.concurrent.CompletableFuture.supplyAsync;

import com.google.common.base.Strings;
import com.moego.common.enums.AppointmentEventEnum;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.common.utils.WeekUtil;
import com.moego.idl.models.activity_log.v1.Resource;
import com.moego.idl.models.appointment.v1.AppointmentCreateDef;
import com.moego.idl.models.appointment.v1.AppointmentCreateForOnlineBookingDef;
import com.moego.idl.models.appointment.v1.AppointmentExtraInfoModel;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.AppointmentNoShowStatus;
import com.moego.idl.models.appointment.v1.AppointmentPaymentStatus;
import com.moego.idl.models.appointment.v1.AppointmentSource;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.appointment.v1.AppointmentUpdateDef;
import com.moego.idl.models.appointment.v1.AppointmentUpdatedBy;
import com.moego.idl.models.appointment.v1.BlockTimeModel;
import com.moego.idl.models.appointment.v1.LodgingAssignInfo;
import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.idl.models.appointment.v1.PetDetailDef;
import com.moego.idl.models.appointment.v1.SelectedServiceDef;
import com.moego.idl.models.appointment.v2.PricingRuleApplySourceType;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessPetSizeModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.fulfillment.v1.ServiceItem;
import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.models.offering.v1.EvaluationModel;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServicePriceUnit;
import com.moego.idl.models.offering.v1.ServiceScopeType;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.models.order.v1.OrderDef;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.models.order.v1.OrderSourceType;
import com.moego.idl.models.organization.v1.StaffModel;
import com.moego.idl.models.organization.v1.TaxRuleModel;
import com.moego.idl.models.organization.v1.TimeRangeDef;
import com.moego.idl.models.organization.v1.WorkingHoursDef;
import com.moego.idl.service.activity_log.v1.ActivityLogServiceGrpc;
import com.moego.idl.service.activity_log.v1.CreateActivityLogRequest;
import com.moego.idl.service.appointment.v1.AppointmentList;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.BatchBookAgainAppointmentRequest;
import com.moego.idl.service.appointment.v1.BatchBookAgainAppointmentResponse;
import com.moego.idl.service.appointment.v1.BatchCancelAppointmentRequest;
import com.moego.idl.service.appointment.v1.BatchCancelAppointmentResponse;
import com.moego.idl.service.appointment.v1.BatchQuickCheckInRequest;
import com.moego.idl.service.appointment.v1.BatchQuickCheckInResponse;
import com.moego.idl.service.appointment.v1.CalculateAppointmentInvoiceRequest;
import com.moego.idl.service.appointment.v1.CalculateAppointmentInvoiceResponse;
import com.moego.idl.service.appointment.v1.CancelAppointmentRequest;
import com.moego.idl.service.appointment.v1.CancelAppointmentResponse;
import com.moego.idl.service.appointment.v1.CountAppointmentForPetsRequest;
import com.moego.idl.service.appointment.v1.CountAppointmentForPetsResponse;
import com.moego.idl.service.appointment.v1.CreateAppointmentForOnlineBookingRequest;
import com.moego.idl.service.appointment.v1.CreateAppointmentForOnlineBookingResponse;
import com.moego.idl.service.appointment.v1.CreateAppointmentRequest;
import com.moego.idl.service.appointment.v1.CreateAppointmentResponse;
import com.moego.idl.service.appointment.v1.CreateBlockRequest;
import com.moego.idl.service.appointment.v1.CreateBlockResponse;
import com.moego.idl.service.appointment.v1.CreateExtraInfoRequest;
import com.moego.idl.service.appointment.v1.CreateExtraInfoResponse;
import com.moego.idl.service.appointment.v1.DeleteAppointmentsRequest;
import com.moego.idl.service.appointment.v1.DeleteAppointmentsResponse;
import com.moego.idl.service.appointment.v1.GetAppointmentListRequest;
import com.moego.idl.service.appointment.v1.GetAppointmentListResponse;
import com.moego.idl.service.appointment.v1.GetAppointmentRequest;
import com.moego.idl.service.appointment.v1.GetAppointmentResponse;
import com.moego.idl.service.appointment.v1.GetCustomerLastAppointmentRequest;
import com.moego.idl.service.appointment.v1.GetCustomerLastAppointmentResponse;
import com.moego.idl.service.appointment.v1.GetInProgressAppointmentRequest;
import com.moego.idl.service.appointment.v1.GetInProgressAppointmentResponse;
import com.moego.idl.service.appointment.v1.GetTimeOverlapAppointmentListRequest;
import com.moego.idl.service.appointment.v1.GetTimeOverlapAppointmentListResponse;
import com.moego.idl.service.appointment.v1.ListAppointmentForPetsRequest;
import com.moego.idl.service.appointment.v1.ListAppointmentForPetsResponse;
import com.moego.idl.service.appointment.v1.ListAppointmentsForCustomersRequest;
import com.moego.idl.service.appointment.v1.ListAppointmentsForCustomersResponse;
import com.moego.idl.service.appointment.v1.ListAppointmentsRequest;
import com.moego.idl.service.appointment.v1.ListAppointmentsResponse;
import com.moego.idl.service.appointment.v1.ListBlockTimesRequest;
import com.moego.idl.service.appointment.v1.ListBlockTimesResponse;
import com.moego.idl.service.appointment.v1.ListExtraInfoRequest;
import com.moego.idl.service.appointment.v1.ListExtraInfoResponse;
import com.moego.idl.service.appointment.v1.PreviewEstimateOrderRequest;
import com.moego.idl.service.appointment.v1.PreviewEstimateOrderResponse;
import com.moego.idl.service.appointment.v1.PreviewOrderDetailRequest;
import com.moego.idl.service.appointment.v1.PreviewOrderDetailResponse;
import com.moego.idl.service.appointment.v1.PreviewOrderLineItemsRequest;
import com.moego.idl.service.appointment.v1.PreviewOrderLineItemsResponse;
import com.moego.idl.service.appointment.v1.RescheduleBoardingAppointmentRequest;
import com.moego.idl.service.appointment.v1.RescheduleBoardingAppointmentResponse;
import com.moego.idl.service.appointment.v1.RestoreAppointmentsRequest;
import com.moego.idl.service.appointment.v1.RestoreAppointmentsResponse;
import com.moego.idl.service.appointment.v1.SyncAppointmentToOrderRequest;
import com.moego.idl.service.appointment.v1.SyncAppointmentToOrderResponse;
import com.moego.idl.service.appointment.v1.UpdateAppointmentRequest;
import com.moego.idl.service.appointment.v1.UpdateAppointmentResponse;
import com.moego.idl.service.appointment.v1.UpdateAppointmentSelectiveRequest;
import com.moego.idl.service.appointment.v1.UpdateAppointmentSelectiveResponse;
import com.moego.idl.service.order.v1.OrderServiceGrpc;
import com.moego.idl.service.order.v1.UpdateOrderIncrRequest;
import com.moego.idl.utils.v1.Int64ListValue;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.proto.MoneyUtils;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.common.util.JsonUtil;
import com.moego.lib.common.util.Tx;
import com.moego.server.grooming.dto.appointment.history.CancelLogDTO;
import com.moego.server.grooming.enums.AppointmentAction;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.params.PreAuthParams;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLogRecorder;
import com.moego.svc.appointment.controller.validate.PetDetailValidator;
import com.moego.svc.appointment.converter.AppointmentConverter;
import com.moego.svc.appointment.converter.DateConverter;
import com.moego.svc.appointment.converter.GroomingNoteConverter;
import com.moego.svc.appointment.converter.PageConverter;
import com.moego.svc.appointment.converter.PetDetailConverter;
import com.moego.svc.appointment.domain.BoardingSplitLodging;
import com.moego.svc.appointment.domain.EvaluationServiceDetail;
import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import com.moego.svc.appointment.domain.MoeGroomingNote;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import com.moego.svc.appointment.domain.MoeGroomingServiceOperation;
import com.moego.svc.appointment.domain.PricingRuleRecordApplyLog;
import com.moego.svc.appointment.domain.ServiceChargeDetail;
import com.moego.svc.appointment.dto.AppointmentPetServiceDTO;
import com.moego.svc.appointment.dto.CancelAppointmentDTO;
import com.moego.svc.appointment.dto.CreateAppointmentResultDTO;
import com.moego.svc.appointment.dto.PetDetailDTO;
import com.moego.svc.appointment.helper.BusinessHelper;
import com.moego.svc.appointment.helper.FeatureFlagHelper;
import com.moego.svc.appointment.listener.event.CreateAppointmentEvent;
import com.moego.svc.appointment.listener.event.CreateBlockEvent;
import com.moego.svc.appointment.service.ApplyServiceChargeService;
import com.moego.svc.appointment.service.AppointmentCompositeService;
import com.moego.svc.appointment.service.AppointmentExtraInfoService;
import com.moego.svc.appointment.service.AppointmentServiceProxy;
import com.moego.svc.appointment.service.BoardingSplitLodgingService;
import com.moego.svc.appointment.service.EvaluationServiceDetailService;
import com.moego.svc.appointment.service.LodgingService;
import com.moego.svc.appointment.service.NoteService;
import com.moego.svc.appointment.service.PetDetailServiceProxy;
import com.moego.svc.appointment.service.PricingRuleRecordApplyService;
import com.moego.svc.appointment.service.RepeatAppointmentService;
import com.moego.svc.appointment.service.ServiceChargeDetailService;
import com.moego.svc.appointment.service.ServiceOperationService;
import com.moego.svc.appointment.service.params.BatchCheckInAutoAssignParam;
import com.moego.svc.appointment.service.remote.ActiveMQService;
import com.moego.svc.appointment.service.remote.CompanyRemoteService;
import com.moego.svc.appointment.service.remote.LodgingRemoteService;
import com.moego.svc.appointment.service.remote.NotificationRemoteService;
import com.moego.svc.appointment.service.remote.OfferingRemoteService;
import com.moego.svc.appointment.service.remote.OrderRemoteService;
import com.moego.svc.appointment.service.remote.OrganizationRemoteService;
import com.moego.svc.appointment.service.remote.PetRemoteService;
import com.moego.svc.appointment.service.remote.ServiceChargeHelper;
import com.moego.svc.appointment.utils.LineItemUtils;
import com.moego.svc.appointment.utils.Pair;
import com.moego.svc.appointment.utils.PetDetailUtil;
import com.moego.svc.appointment.utils.ServiceChargeUtil;
import io.grpc.stub.StreamObserver;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.transaction.support.TransactionOperations;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2024/1/24
 */
@Slf4j
@GrpcService
@RequiredArgsConstructor
public class AppointmentController extends AppointmentServiceGrpc.AppointmentServiceImplBase {

    private final AppointmentCompositeService appointmentCompositeService;
    private final AppointmentServiceProxy appointmentService;
    private final PetDetailServiceProxy petDetailService;
    private final EvaluationServiceDetailService evaluationService;
    private final NoteService noteService;
    private final PricingRuleRecordApplyService pricingRuleApplyService;
    private final RepeatAppointmentService repeatAppointmentService;
    private final ServiceOperationService serviceOperationService;
    private final BoardingSplitLodgingService boardingSplitLodgingService;

    private final PetRemoteService petRemoteService;
    private final OfferingRemoteService offeringRemoteService;
    private final OrderRemoteService orderRemoteService;
    private final OrganizationRemoteService organizationRemoteService;

    private final ActiveMQService mqService;

    private final ApplicationEventPublisher publisher;
    private final ActivityLogServiceGrpc.ActivityLogServiceBlockingStub activityLogService;
    private final OrderServiceGrpc.OrderServiceBlockingStub orderStub;
    private final TransactionOperations transaction;
    private final NotificationRemoteService notificationRemoteService;
    private final LodgingService lodgingService;
    private final LodgingRemoteService lodgingRemoteService;

    private final PetDetailValidator petDetailValidator;
    private final CompanyRemoteService companyRemoteService;
    private final FeatureFlagHelper featureFlagHelper;
    private final AppointmentExtraInfoService appointmentExtraInfoService;
    private final ServiceChargeDetailService serviceChargeDetailService;
    private final ServiceChargeHelper serviceChargeHelper;
    private final PricingRuleRecordApplyService pricingRuleRecordApplyService;
    private final ApplyServiceChargeService applyServiceChargeService;
    private final BusinessHelper businessHelper;

    private enum CheckInAction {
        SKIP,
        DIRECT_CHECK_IN,
        ADD_SERVICE_AND_CHECK_IN,
        CREATE_APPOINTMENT_AND_CHECK_IN
    }

    @Override
    public void createAppointment(
            CreateAppointmentRequest request, StreamObserver<CreateAppointmentResponse> responseObserver) {
        petDetailValidator.validatePetDetails(request.getPetDetailsList());
        // 1. 根据类型校验参数，动态构建 pet details
        Map<Long, Map<Long, CustomizedServiceView>> petServiceMap = offeringRemoteService.listService(
                request.getCompanyId(), request.getBusinessId(), request.getPetDetailsList());
        List<PetDetailDTO> detailDTOList = petDetailService.buildAllInOnePetDetailList(
                request.getCompanyId(), request.getBusinessId(), request.getPetDetailsList(), petServiceMap, Map.of());
        List<MoeGroomingPetDetail> petDetails =
                detailDTOList.stream().map(PetDetailDTO::getPetDetail).toList();
        // 2. 构建 evaluation list
        List<EvaluationServiceDetail> evaluationList =
                evaluationService.buildEvaluationList(request.getPetDetailsList());

        // 3. 创建 appointment、 pet details、 evaluation details
        MoeGroomingAppointment appointment = buildNewAppointment(request, petDetails, evaluationList);
        CreateAppointmentResultDTO createAppointmentResultDTO =
                appointmentCompositeService.createAppointment(appointment, detailDTOList, evaluationList);
        if (!createAppointmentResultDTO.isNewRecordInserted()) {
            responseObserver.onNext(CreateAppointmentResponse.newBuilder()
                    .setAppointmentId(createAppointmentResultDTO.getAppointmentId())
                    .build());
            responseObserver.onCompleted();
            return;
        }

        appointment.setId(Math.toIntExact(createAppointmentResultDTO.getAppointmentId()));

        // 3.1 Apply pricing rule
        pricingRuleApplyService.applyPricingRule(
                appointment.getId().longValue(), request.getCompanyId(), request.getBusinessId());

        // 4. 创建 order
        Map<Long, CustomizedServiceView> serviceModelMap = detailDTOList.stream()
                .map(PetDetailDTO::getService)
                .collect(Collectors.toMap(CustomizedServiceView::getId, Function.identity(), (s1, s2) -> s1));
        var orderId = orderRemoteService.createOrder(
                appointment,
                petDetailService.getPetDetailList(appointment.getId().longValue()),
                evaluationService.getPetEvaluationList(appointment.getId().longValue()),
                serviceModelMap,
                request.hasPreAuth() && request.getPreAuth().getEnable());
        if (orderId != null) {
            appointment.setOrderId(String.valueOf(orderId));

            var update = new MoeGroomingAppointment();
            update.setId(appointment.getId());
            update.setOrderId(String.valueOf(orderId));
            appointmentService.update(update);
        }

        // 5. 保存 appointment notes
        List<MoeGroomingNote> notes = GroomingNoteConverter.INSTANCE.defToDomain(request.getNotesList());
        notes.forEach(note -> {
            note.setCompanyId(request.getCompanyId());
            note.setBusinessId((int) request.getBusinessId());
            note.setCustomerId((int) request.getAppointment().getCustomerId());
            note.setGroomingId(appointment.getId());
            note.setCreateBy((int) request.getStaffId());
            note.setUpdateBy((int) request.getStaffId());
            note.setCreateTime(CommonUtil.get10Timestamp());
            note.setUpdateTime(CommonUtil.get10Timestamp());
        });
        noteService.insertMultiple(notes);

        // 6. Pre-auth need push to message queue
        mqService.publishAppointmentEvent(
                new PreAuthParams(
                        request.getPreAuth().getEnable(),
                        request.getPreAuth().getPaymentMethodId(),
                        request.getPreAuth().getCardBrandLast4()),
                appointment,
                AppointmentEventEnum.CREATE_SINGLE);

        // 7. 发布创建 appointment 的事件
        publisher.publishEvent(new CreateAppointmentEvent(this)
                .setCompanyId(request.getCompanyId())
                .setBusinessId(request.getBusinessId())
                .setStaffId(request.getStaffId())
                .setCustomerId(request.getAppointment().getCustomerId())
                .setAppointmentId(Long.valueOf(appointment.getId()))
                .setPetDetailDefs(request.getPetDetailsList())
                .setAppointment(appointment));

        responseObserver.onNext(CreateAppointmentResponse.newBuilder()
                .setAppointmentId(appointment.getId())
                .build());
        responseObserver.onCompleted();
    }

    /**
     * Build new appointment model
     * calculate appointment date and time from pet details
     *
     * @param request    appointment params
     * @param petDetails pet details
     * @return new appointment model
     */
    public MoeGroomingAppointment buildNewAppointment(
            CreateAppointmentRequest request,
            List<MoeGroomingPetDetail> petDetails,
            List<EvaluationServiceDetail> evaluationList) {
        AppointmentCreateDef appointmentCreateDef = request.getAppointment();
        MoeGroomingAppointment appointment = new MoeGroomingAppointment();
        appointment.setOrderId(CommonUtil.getUuid());
        appointment.setBusinessId((int) request.getBusinessId());
        appointment.setCompanyId(request.getCompanyId());
        appointment.setCustomerId((int) appointmentCreateDef.getCustomerId());
        appointment.setStatus((byte) AppointmentStatus.UNCONFIRMED_VALUE);
        appointment.setIsPaid((byte) AppointmentPaymentStatus.UNPAID_VALUE);
        appointment.setColorCode(appointmentCreateDef.getColorCode());
        appointment.setSource(appointmentCreateDef.getSourceValue());
        appointment.setCreatedById((int) request.getStaffId());
        appointment.setUpdatedById(request.getStaffId());
        // 从 petDetails 里动态获取计算最小和最大值
        Pair<LocalDateTime, LocalDateTime> pair =
                petDetailService.calculatePeriod(request.getCompanyId(), petDetails, evaluationList);
        appointment.setAppointmentDate(pair.first().toLocalDate().toString());
        appointment.setAppointmentEndDate(pair.second().toLocalDate().toString());
        appointment.setAppointmentStartTime(pair.first().toLocalTime().toSecondOfDay() / 60);
        appointment.setAppointmentEndTime(pair.second().toLocalTime().toSecondOfDay() / 60);
        appointment.setServiceTypeInclude(PetDetailUtil.calculateServiceTypeInclude(petDetails, evaluationList));

        long createTime = request.hasCreatedAt() ? request.getCreatedAt().getSeconds() : CommonUtil.get10Timestamp();
        appointment.setCreateTime(createTime);
        appointment.setUpdateTime(createTime);
        return appointment;
    }

    /**
     * Build new appointment model for online booking
     * calculate appointment date and time from pet details
     *
     * @param request    appointment params
     * @param petDetails pet details
     * @return new appointment model
     */
    public MoeGroomingAppointment buildNewAppointmentForOnlineBooking(
            CreateAppointmentForOnlineBookingRequest request,
            List<MoeGroomingPetDetail> petDetails,
            List<EvaluationServiceDetail> evaluationList) {
        AppointmentCreateForOnlineBookingDef appointmentCreateDef = request.getAppointment();
        MoeGroomingAppointment appointment = new MoeGroomingAppointment();
        appointment.setOrderId(CommonUtil.getUuid());
        appointment.setBusinessId((int) request.getBusinessId());
        appointment.setCompanyId(request.getCompanyId());
        appointment.setCustomerId((int) appointmentCreateDef.getCustomerId());
        appointment.setStatus(
                appointmentCreateDef.hasStatus()
                        ? (byte) appointmentCreateDef.getStatus().getNumber()
                        : (byte) AppointmentStatus.UNCONFIRMED_VALUE);
        appointment.setIsPaid((byte) AppointmentPaymentStatus.UNPAID_VALUE);
        appointment.setColorCode("#000000");
        appointment.setSource(AppointmentSource.ONLINE_BOOKING.getNumber());
        appointment.setCreatedById((int) request.getStaffId());
        appointment.setUpdatedById(request.getStaffId());
        appointment.setCreateTime(appointmentCreateDef.getCreatedAt().getSeconds());
        appointment.setUpdateTime(CommonUtil.get10Timestamp());
        // 从 petDetails 里动态获取计算最小和最大值
        Pair<LocalDateTime, LocalDateTime> pair =
                petDetailService.calculatePeriod(request.getCompanyId(), petDetails, evaluationList);
        appointment.setAppointmentDate(pair.first().toLocalDate().toString());
        appointment.setAppointmentEndDate(pair.second().toLocalDate().toString());
        appointment.setAppointmentStartTime(pair.first().toLocalTime().toSecondOfDay() / 60);
        appointment.setAppointmentEndTime(pair.second().toLocalTime().toSecondOfDay() / 60);
        appointment.setServiceTypeInclude(PetDetailUtil.calculateServiceTypeInclude(petDetails, evaluationList));
        appointment.setCancelBy(
                appointmentCreateDef.hasCancelBy() ? Math.toIntExact(appointmentCreateDef.getCancelBy()) : 0);
        appointment.setSource(
                appointmentCreateDef.hasSource()
                        ? appointmentCreateDef.getSourceValue()
                        : AppointmentSource.ONLINE_BOOKING.getNumber());

        return appointment;
    }

    private void updateColor(UpdateAppointmentRequest request) {
        String colorCode = request.getAppointment().getColorCode();
        MoeGroomingAppointment appointment = new MoeGroomingAppointment();
        appointment.setColorCode(colorCode);
        appointment.setId((int) request.getAppointmentId());
        appointment.setCompanyId(request.getCompanyId());
        appointment.setBusinessId((int) request.getBusinessId());
        appointmentService.updateByAppointmentId(appointment);
        ActivityLogRecorder.record(
                request.getBusinessId(),
                request.getStaffId(),
                AppointmentAction.UPDATE_COLOR,
                ResourceType.APPOINTMENT,
                request.getAppointmentId(),
                colorCode);
    }

    private void switchAllPetsStartAtSameTime(UpdateAppointmentRequest request) {
        MoeGroomingAppointment beforeAppointment =
                appointmentService.getAppointment(request.getCompanyId(), request.getAppointmentId());
        // 目前 evaluation 服务不支持 allPetsStartAtSameTime
        if (ServiceItemEnum.EVALUATION.isIncludedIn(beforeAppointment.getServiceTypeInclude())) {
            throw bizException(
                    Code.CODE_PARAMS_ERROR,
                    "Can not switch all pets start at the same time when there is evaluation service");
        }
        // 1. 更新 pet details
        petDetailService.recalculatePetDetailStartTime(
                request.getAppointmentId(), request.getAppointment().getAllPetsStartAtSameTime());
        // 2. 刷新 appointment 时间
        appointmentService.refreshAppointmentDateTime(beforeAppointment);

        ActivityLogRecorder.record(
                request.getBusinessId(),
                request.getStaffId(),
                AppointmentAction.MULTI_PETS_START_AT_THE_SAME_TIME,
                ResourceType.APPOINTMENT,
                request.getAppointmentId(),
                request.getAppointment().getAllPetsStartAtSameTime());
    }

    @Override
    public void updateAppointment(
            UpdateAppointmentRequest request, StreamObserver<UpdateAppointmentResponse> responseObserver) {
        AppointmentUpdateDef updateDef = request.getAppointment();
        if (updateDef.hasColorCode()) {
            updateColor(request);
        }
        if (updateDef.hasAllPetsStartAtSameTime()) {
            switchAllPetsStartAtSameTime(request);
        }

        responseObserver.onNext(UpdateAppointmentResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void updateAppointmentSelective(
            UpdateAppointmentSelectiveRequest request,
            StreamObserver<UpdateAppointmentSelectiveResponse> responseObserver) {

        var beforeAppointment = appointmentService.mustGet(request.getId());
        var beforePetDetails = petDetailService.getPetDetailList(request.getId());
        var beforePetDetailsMap =
                beforePetDetails.stream().collect(Collectors.toMap(MoeGroomingPetDetail::getId, v -> v));
        var boardingSplitLodgings = boardingSplitLodgingService.getBoardingSplitLodgings(
                List.of(beforeAppointment.getId().longValue()));

        Integer affectedRows = transaction.execute(status -> {
            var appointmentAffectedRows =
                    appointmentService.update(AppointmentConverter.INSTANCE.updateRequestToEntity(request));
            for (var petDetailUpdate : request.getPetDetailsList()) {
                var beforePetDetail = beforePetDetailsMap.get((int) petDetailUpdate.getId());
                if (beforePetDetail == null) {
                    throw bizException(Code.CODE_PARAMS_ERROR, "Pet detail not found");
                }
                var updateBean = PetDetailConverter.INSTANCE.toDomain(petDetailUpdate, beforePetDetail);
                if (updateBean.getDateType() == PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE) {
                    updateBean.setServiceTime(PetDetailUtil.calculateOffsetMinute(
                            updateBean.getStartDate(),
                            updateBean.getStartTime().intValue(),
                            updateBean.getEndDate(),
                            updateBean.getEndTime().intValue()));
                } else {
                    updateBean.setServiceTime((int) (updateBean.getEndTime() - updateBean.getStartTime()));
                }

                //  更新 split lodging
                var newUpdatePetDetail = lodgingService.updateSplitLodging(
                        beforeAppointment, boardingSplitLodgings, beforePetDetail, updateBean);

                petDetailService.update(newUpdatePetDetail);
            }
            return appointmentAffectedRows;
        });

        //        // 刷新预约时间
        //        appointmentService.refreshAppointmentDateTime(beforeAppointment);
        var newAppointment = appointmentService.mustGet(request.getId());
        if (!CollectionUtils.isEmpty(request.getPetDetailsList())) {
            var newPetDetails = petDetailService.getPetDetailList(request.getId());
            // 重新应用 pricing rule
            pricingRuleApplyService.applyPricingRule(
                    request.getId(),
                    beforeAppointment.getCompanyId(),
                    beforeAppointment.getBusinessId().longValue());

            // 更新 order
            if (PetDetailUtil.needUpdateOrder(beforePetDetails, newPetDetails)) {
                orderRemoteService.updateOrder(newAppointment);
            }
        }
        // 发送 appointment event（pre-auth）
        mqService.publishAppointmentEvent(newAppointment, AppointmentEventEnum.MODIFY_SINGLE);

        // C 端变更需要给 B 端发送 notification
        ThreadPool.execute(() -> {
            boolean updatedAppt = !Objects.equals(
                            newAppointment.getAppointmentDate(), beforeAppointment.getAppointmentDate())
                    || !Objects.equals(
                            newAppointment.getAppointmentStartTime(), beforeAppointment.getAppointmentStartTime());
            if (updatedAppt && AppointmentUpdatedBy.BY_CLIENT_PORTAL.equals(request.getUpdateByType())) {
                notificationRemoteService.notificationApptRescheduleToAllStaff(newAppointment);
            }
        });

        responseObserver.onNext(UpdateAppointmentSelectiveResponse.newBuilder()
                .setAffectedRows((affectedRows != null) ? affectedRows : 0)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void getAppointment(GetAppointmentRequest request, StreamObserver<GetAppointmentResponse> responseObserver) {
        Long companyId = null;
        // 将 companyId 改为可选参数
        if (request.getCompanyId() != 0) {
            companyId = request.getCompanyId();
        }
        long appointmentId = request.getAppointmentId();
        MoeGroomingAppointment appointment = appointmentService.getAppointment(companyId, appointmentId);
        var isNewOrder = appointmentExtraInfoService.isNewOrder(appointmentId);

        responseObserver.onNext(GetAppointmentResponse.newBuilder()
                .setAppointment(AppointmentConverter.INSTANCE.toModel(appointment).toBuilder()
                        .setIsNewOrder(isNewOrder)
                        .build())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void getAppointmentList(
            GetAppointmentListRequest request, StreamObserver<GetAppointmentListResponse> responseObserver) {
        long companyId = request.getCompanyId();
        List<Long> appointmentIds = request.getAppointmentIdList();
        if (CollectionUtils.isEmpty(appointmentIds)) {
            responseObserver.onNext(GetAppointmentListResponse.newBuilder().build());
            responseObserver.onCompleted();
            return;
        }

        List<MoeGroomingAppointment> appointments = appointmentService.getAppointments(companyId, appointmentIds);
        var idToNewOrder = appointmentExtraInfoService.listNewOrderFlag(appointmentIds);

        responseObserver.onNext(GetAppointmentListResponse.newBuilder()
                .addAllAppointments(AppointmentConverter.INSTANCE.toModel(appointments).stream()
                        .map(appointment -> appointment.toBuilder()
                                .setIsNewOrder(idToNewOrder.getOrDefault(appointment.getId(), false))
                                .build())
                        .toList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void getCustomerLastAppointment(
            GetCustomerLastAppointmentRequest request,
            StreamObserver<GetCustomerLastAppointmentResponse> responseObserver) {
        Map<Long, MoeGroomingAppointment> customerLastAppointment = appointmentService.getCustomerLastAppointment(
                request.getCompanyId(), request.getCustomerIdList(), request.getStatus(), request.getFilter());

        Map<Long, AppointmentModel> collect = customerLastAppointment.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey, entry -> AppointmentConverter.INSTANCE.toModel(entry.getValue())));

        responseObserver.onNext(GetCustomerLastAppointmentResponse.newBuilder()
                .putAllCustomerLastAppointment(collect)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void calculateAppointmentInvoice(
            CalculateAppointmentInvoiceRequest request,
            StreamObserver<CalculateAppointmentInvoiceResponse> responseObserver) {
        Map<Long, Map<Long, CustomizedServiceView>> petServiceMap = offeringRemoteService.listService(
                request.getCompanyId(), request.getBusinessId(), request.getPetDetailsList());
        BigDecimal servicePrice = appointmentService.calculateServicePrice(
                request.getCompanyId(), request.getBusinessId(), request.getPetDetailsList(), petServiceMap);
        BigDecimal serviceChargePrice = applyServiceChargeService.calculateServiceChargeAmount(
                request.getCompanyId(), request.getBusinessId(), request.getPetDetailsList());

        responseObserver.onNext(CalculateAppointmentInvoiceResponse.newBuilder()
                .setOrder(OrderDef.newBuilder()
                        .setEstimatedTotal(servicePrice.add(serviceChargePrice).doubleValue())
                        .build())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void previewEstimateOrder(
            PreviewEstimateOrderRequest request, StreamObserver<PreviewEstimateOrderResponse> responseObserver) {
        var petDetails = petDetailService.getPetDetailList(request.getAppointmentIdsList());
        petDetailService.fixServiceType(request.getCompanyId(), petDetails);
        var evaluations = evaluationService.getPetEvaluationList(request.getAppointmentIdsList());
        var currency = organizationRemoteService.getCurrency(request.getCompanyId());
        var appointmentIdToPetDetails =
                petDetails.stream().collect(Collectors.groupingBy(MoeGroomingPetDetail::getGroomingId));
        var appointmentIdToEvaluations =
                evaluations.stream().collect(Collectors.groupingBy(EvaluationServiceDetail::getAppointmentId));
        var serviceChargeDetails = serviceChargeDetailService.listByAppointmentIds(request.getAppointmentIdsList());

        var appointmentIdToServiceChargeDetails =
                serviceChargeDetails.stream().collect(Collectors.groupingBy(ServiceChargeDetail::getAppointmentId));

        var estimatedOrders = request.getAppointmentIdsList().stream()
                .map(appointmentId -> {
                    var servicesSubtotal = PetDetailUtil.calculateAmount(
                            appointmentIdToPetDetails.getOrDefault(appointmentId.intValue(), List.of()),
                            appointmentIdToEvaluations.getOrDefault(appointmentId, List.of()));

                    var serviceChargeTotal = ServiceChargeUtil.calculateAmount(
                            appointmentIdToServiceChargeDetails.getOrDefault(appointmentId, List.of()));

                    return PreviewEstimateOrderResponse.EstimatedOrder.newBuilder()
                            .setAppointmentId(appointmentId)
                            .setServicesSubtotal(MoneyUtils.toGoogleMoney(servicesSubtotal, currency))
                            .setServicesChargesTotal(MoneyUtils.toGoogleMoney(serviceChargeTotal, currency))
                            .setEstimatedTotal(
                                    MoneyUtils.toGoogleMoney(servicesSubtotal.add(serviceChargeTotal), currency))
                            .build();
                })
                .toList();

        responseObserver.onNext(PreviewEstimateOrderResponse.newBuilder()
                .addAllEstimatedOrders(estimatedOrders)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void getInProgressAppointment(
            GetInProgressAppointmentRequest request,
            StreamObserver<GetInProgressAppointmentResponse> responseObserver) {
        MoeGroomingAppointment appointment = appointmentService.selectInProgressAppointment(
                request.getCompanyId(),
                request.hasBusinessId() ? Long.valueOf(request.getBusinessId()).intValue() : null,
                request.hasCustomerId() ? Long.valueOf(request.getCustomerId()).intValue() : null,
                request.hasPetId() ? Long.valueOf(request.getPetId()).intValue() : null,
                request.getServiceItemType());

        var response = GetInProgressAppointmentResponse.newBuilder();
        if (appointment != null) {
            response.setAppointmentId(appointment.getId());
        }
        responseObserver.onNext(response.build());
        responseObserver.onCompleted();
    }

    @Override
    public void createAppointmentForOnlineBooking(
            CreateAppointmentForOnlineBookingRequest request,
            StreamObserver<CreateAppointmentForOnlineBookingResponse> responseObserver) {
        // 1. 根据类型校验参数，动态构建 pet details
        Map<Long, Map<Long, CustomizedServiceView>> petServiceMap = offeringRemoteService.listService(
                request.getCompanyId(), request.getBusinessId(), request.getPetDetailsList());
        List<PetDetailDTO> detailDTOList = petDetailService.buildAllInOnePetDetailList(
                request.getCompanyId(), request.getBusinessId(), request.getPetDetailsList(), petServiceMap, Map.of());
        List<PetDetailDTO> newDetailDTOList = pricingRuleApplyService.updatePetDetailByOnlineBooking(
                request.getCompanyId(),
                request.getBookingRequestId(),
                PricingRuleApplySourceType.SOURCE_TYPE_BOOKING_REQUEST,
                detailDTOList);

        // 2. 构建 evaluation list
        List<EvaluationServiceDetail> evaluationList =
                evaluationService.buildEvaluationList(request.getPetDetailsList());

        // 3. 创建 appointment、 pet details、 evaluation details
        List<MoeGroomingPetDetail> petDetails =
                newDetailDTOList.stream().map(PetDetailDTO::getPetDetail).toList();
        MoeGroomingAppointment appointment = buildNewAppointmentForOnlineBooking(request, petDetails, evaluationList);
        CreateAppointmentResultDTO createAppointmentResultDTO =
                appointmentCompositeService.createAppointment(appointment, newDetailDTOList, evaluationList);
        if (!createAppointmentResultDTO.isNewRecordInserted()) {
            responseObserver.onNext(CreateAppointmentForOnlineBookingResponse.newBuilder()
                    .setAppointmentId(createAppointmentResultDTO.getAppointmentId())
                    .build());
            responseObserver.onCompleted();
            return;
        }

        appointment.setId(Math.toIntExact(createAppointmentResultDTO.getAppointmentId()));

        // 4. 创建 order
        // BD booking request 如果开启了 prepay，会在创建 booking request 之后创建 order，
        // 在将 booking request 转换为 appointment 时，需要将 order 的 sourceType 和 sourceId 更新为 appointment
        var order = orderRemoteService.getBySource(OrderSourceType.BOOKING_REQUEST, request.getBookingRequestId());

        log.info("order: {}", order);

        try {

            if (order == null) {
                createOrder(request, newDetailDTOList, appointment);
            } else {
                // 修改 order 的 sourceType 和 sourceId
                updateOrderSource(order.getOrder().getId(), OrderSourceType.APPOINTMENT, appointment.getId());
            }

            // 5. 保存 appointment notes
            addAppointmentNotes(request, appointment);

            // 6. 更新 pricing rule log
            pricingRuleApplyService.copyApplyLog(
                    request.getCompanyId(),
                    request.getBusinessId(),
                    request.getBookingRequestId(),
                    PricingRuleApplySourceType.SOURCE_TYPE_BOOKING_REQUEST,
                    appointment.getId().longValue(),
                    PricingRuleApplySourceType.SOURCE_TYPE_APPOINTMENT,
                    appointment.getAppointmentDate(),
                    appointment.getAppointmentEndDate());
            // 7. 发布创建 appointment 的事件
            if (needPublishEvent(request.getAppointment())) {
                publisher.publishEvent(new CreateAppointmentEvent(this)
                        .setCompanyId(request.getCompanyId())
                        .setBusinessId(request.getBusinessId())
                        .setStaffId(request.getStaffId())
                        .setCustomerId(request.getAppointment().getCustomerId())
                        .setAppointmentId(Long.valueOf(appointment.getId()))
                        .setPetDetailDefs(request.getPetDetailsList())
                        .setAppointment(appointment)
                        .setIsBookingRequest(true));
            }
        } catch (Exception e) {

            // 回滚 order sourceType 和 sourceId
            if (order != null) {
                updateOrderSource(
                        order.getOrder().getId(), OrderSourceType.BOOKING_REQUEST, request.getBookingRequestId());
            }

            // 回滚 pricing rule log
            pricingRuleApplyService.removeApplyLog(
                    request.getCompanyId(),
                    request.getBusinessId(),
                    appointment.getId().longValue(),
                    PricingRuleApplySourceType.SOURCE_TYPE_APPOINTMENT);

            throw e;
        }

        responseObserver.onNext(CreateAppointmentForOnlineBookingResponse.newBuilder()
                .setAppointmentId(appointment.getId())
                .build());
        responseObserver.onCompleted();
    }

    /**
     * 仅创建 unconfirmed status 的 appt 才需要发送事件，其他状态的 appt 不需要发送事件
     *
     * @param appointmentDef appointment info
     * @return true if need publish event
     */
    private static boolean needPublishEvent(AppointmentCreateForOnlineBookingDef appointmentDef) {
        return appointmentDef.hasStatus() && appointmentDef.getStatus() == AppointmentStatus.UNCONFIRMED;
    }

    private void addAppointmentNotes(
            CreateAppointmentForOnlineBookingRequest request, MoeGroomingAppointment appointment) {
        List<MoeGroomingNote> notes = GroomingNoteConverter.INSTANCE.defToDomain(request.getNotesList());
        notes.forEach(note -> {
            note.setCompanyId(request.getCompanyId());
            note.setBusinessId((int) request.getBusinessId());
            note.setCustomerId((int) request.getAppointment().getCustomerId());
            note.setGroomingId(appointment.getId());
            note.setCreateBy((int) request.getStaffId());
            note.setUpdateBy((int) request.getStaffId());
            note.setCreateTime(CommonUtil.get10Timestamp());
            note.setUpdateTime(CommonUtil.get10Timestamp());
        });
        noteService.insertMultiple(notes);
    }

    private void createOrder(
            CreateAppointmentForOnlineBookingRequest request,
            List<PetDetailDTO> petDetails,
            MoeGroomingAppointment appointment) {

        pricingRuleApplyService.applyPricingRule(
                appointment.getId().longValue(), request.getCompanyId(), request.getBusinessId());

        Map<Long, CustomizedServiceView> serviceModelMap = petDetails.stream()
                .map(PetDetailDTO::getService)
                .collect(Collectors.toMap(CustomizedServiceView::getId, Function.identity(), (s1, s2) -> s1));

        orderRemoteService.createOrder(
                appointment,
                petDetailService.getPetDetailList(appointment.getId().longValue()),
                evaluationService.getPetEvaluationList(appointment.getId().longValue()),
                serviceModelMap,
                false);
    }

    private void updateOrderSource(long orderId, OrderSourceType sourceType, long sourceId) {
        orderStub.updateOrderIncremental(UpdateOrderIncrRequest.newBuilder()
                .setOrderId(orderId)
                .setOrder(OrderModel.newBuilder()
                        .setSourceType(sourceType.name().toLowerCase())
                        .setSourceId(sourceId)
                        .build())
                .build());
    }

    @Override
    public void batchQuickCheckIn(
            BatchQuickCheckInRequest request, StreamObserver<BatchQuickCheckInResponse> responseObserver) {
        var petIdToAppointmentsMap = appointmentService.listAppointmentsForPet(
                request.getCompanyId(),
                new HashSet<>(request.getPetIdsList()),
                ListAppointmentForPetsRequest.Filter.newBuilder()
                        .setDate(request.getDate())
                        .addAllStatuses(ACTIVE_STATUS_SET)
                        .setBusinessId(request.getBusinessId())
                        .build());

        var petIdToCheckInActionMap = getPetIdToCheckInAction(petIdToAppointmentsMap, request.getServiceId());

        List<Long> petIdsNeedToCreateAppointment = new ArrayList<>();
        Set<Long> directCheckInAppointmentIds = new HashSet<>();
        Set<AppointmentPetServiceDTO> appointmentPetServices = new HashSet<>();
        petIdToCheckInActionMap.forEach((petId, checkInAction) -> {
            var action = checkInAction.first();
            var appointment = checkInAction.second();
            switch (action) {
                case SKIP -> log.warn("Skip check in for pet {}", petId);
                case DIRECT_CHECK_IN -> directCheckInAppointmentIds.add(
                        appointment.getId().longValue());
                case ADD_SERVICE_AND_CHECK_IN -> appointmentPetServices.add(new AppointmentPetServiceDTO()
                        .setAppointmentId(appointment.getId().longValue())
                        .setPetId(petId)
                        .setServiceId(request.getServiceId())
                        .setStartDate(DateConverter.INSTANCE
                                .fromGoogleDate(request.getDate())
                                .toString())
                        .setCustomerId(appointment.getCustomerId().longValue()));
                case CREATE_APPOINTMENT_AND_CHECK_IN -> petIdsNeedToCreateAppointment.add(petId);
            }
        });

        var createdAppointmentIdList = batchCreateAppointmentForQuickCheckIn(request, petIdsNeedToCreateAppointment);

        appointmentService.batchCheckIn(directCheckInAppointmentIds);

        appointmentCompositeService.batchAddServiceAndCheckIn(
                request.getCompanyId(), request.getBusinessId(), appointmentPetServices);

        responseObserver.onNext(BatchQuickCheckInResponse.newBuilder()
                .addAllCreatedAppointmentIds(createdAppointmentIdList)
                .addAllUpdatedAppointmentIds(Stream.concat(
                                directCheckInAppointmentIds.stream(),
                                appointmentPetServices.stream().map(AppointmentPetServiceDTO::getAppointmentId))
                        .collect(Collectors.toSet()))
                .build());
        responseObserver.onCompleted();
    }

    /**
     * feat: <a href="https://moego.atlassian.net/browse/MER-876">MER-876</a> <br>
     * 1. skipCheckIn: 有指定 service 的 appointment 且已经 check in 时 <br>
     * 2. directCheckIn: 有指定 service 的 appointment 且未 check in 时 <br>
     * 3. addServiceAndCheckIn: 有 grooming only 的 appointment 且未 check in 时 <br>
     * 4. else: create a new appointment <br>
     *
     * @param petIdToAppointments pet id to appointment with pet details
     * @param serviceId           check in service id
     * @return pet id to check in action
     */
    private static Map<Long, Pair<CheckInAction, MoeGroomingAppointment>> getPetIdToCheckInAction(
            Map<Long, List<Pair<MoeGroomingAppointment, List<MoeGroomingPetDetail>>>> petIdToAppointments,
            long serviceId) {
        return petIdToAppointments.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> {
            var appointments = entry.getValue();
            if (skipCheckIn(appointments, serviceId)) {
                return Pair.of(CheckInAction.SKIP, null);
            } else if (directCheckIn(appointments, serviceId)) {
                return Pair.of(CheckInAction.DIRECT_CHECK_IN, getDirectCheckInAppointment(appointments, serviceId));
            } else if (addServiceAndCheckIn(appointments, serviceId)) {
                return Pair.of(
                        CheckInAction.ADD_SERVICE_AND_CHECK_IN,
                        getAddServiceAndCheckInAppointment(appointments, serviceId));
            }
            return Pair.of(CheckInAction.CREATE_APPOINTMENT_AND_CHECK_IN, null);
        }));
    }

    private static boolean skipCheckIn(
            List<Pair<MoeGroomingAppointment, List<MoeGroomingPetDetail>>> appointments, long serviceId) {
        return appointments.stream()
                .anyMatch(pair -> pair.second().stream()
                                .anyMatch(petDetail ->
                                        Objects.equals(petDetail.getServiceId().longValue(), serviceId))
                        && Set.of(
                                        AppointmentStatus.READY_VALUE,
                                        AppointmentStatus.CHECKED_IN_VALUE,
                                        AppointmentStatus.FINISHED_VALUE)
                                .contains(pair.first().getStatus().intValue()));
    }

    private static boolean directCheckIn(
            Pair<MoeGroomingAppointment, List<MoeGroomingPetDetail>> pair, long serviceId) {
        return pair.second().stream()
                        .anyMatch(petDetail ->
                                Objects.equals(petDetail.getServiceId().longValue(), serviceId))
                && EXPECTED_STATUS_SET.contains(pair.first().getStatus());
    }

    private static boolean directCheckIn(
            List<Pair<MoeGroomingAppointment, List<MoeGroomingPetDetail>>> appointments, long serviceId) {
        return appointments.stream().anyMatch(pair -> directCheckIn(pair, serviceId));
    }

    private static MoeGroomingAppointment getDirectCheckInAppointment(
            List<Pair<MoeGroomingAppointment, List<MoeGroomingPetDetail>>> appointments, long serviceId) {
        return appointments.stream()
                .filter(pair -> directCheckIn(pair, serviceId))
                .findFirst()
                .map(Pair::first)
                .orElse(null);
    }

    private static boolean addServiceAndCheckIn(
            Pair<MoeGroomingAppointment, List<MoeGroomingPetDetail>> pair, long serviceId) {
        return pair.second().stream()
                        .noneMatch(petDetail ->
                                Objects.equals(petDetail.getServiceId().longValue(), serviceId))
                && Objects.equals(pair.first().getServiceTypeInclude(), ServiceItemEnum.GROOMING.getBitValue())
                && EXPECTED_STATUS_SET.contains(pair.first().getStatus());
    }

    private static boolean addServiceAndCheckIn(
            List<Pair<MoeGroomingAppointment, List<MoeGroomingPetDetail>>> appointments, long serviceId) {
        return appointments.stream().anyMatch(pair -> addServiceAndCheckIn(pair, serviceId));
    }

    private static MoeGroomingAppointment getAddServiceAndCheckInAppointment(
            List<Pair<MoeGroomingAppointment, List<MoeGroomingPetDetail>>> appointments, long serviceId) {
        return appointments.stream()
                .filter(pair -> addServiceAndCheckIn(pair, serviceId))
                .findFirst()
                .map(Pair::first)
                .orElse(null);
    }

    private List<Long> batchCreateAppointmentForQuickCheckIn(BatchQuickCheckInRequest request, List<Long> petIdList) {
        if (petIdList.isEmpty()) {
            return List.of();
        }
        var petIdToCustomerId = petRemoteService.getPetCustomerMap(request.getCompanyId(), petIdList);
        var customerIdList = petIdToCustomerId.values().stream().distinct().toList();
        Map<Long, Map<Long, CustomizedServiceView>> petServiceMap = offeringRemoteService.listService(
                request.getCompanyId(),
                request.getBusinessId(),
                petIdList.stream()
                        .collect(Collectors.toMap(
                                key -> key, // keyMapper: 使用列表中的元素作为键
                                key -> List.of(request.getServiceId()) // valueMapper: 使用相同的 Long 值作为所有键的值
                                )));
        Integer startTime = getNowMinuteForCompany(request.getCompanyId());
        Integer serviceDuration = petServiceMap
                .get(petIdList.get(0))
                .get(request.getServiceId())
                .getMaxDuration(); // 因为 Max Duration 与 Pet 无关，因此取第一个 Pet 的时间即可
        Integer endTime = calculateAppointmentEndTime(
                request.getCompanyId(), request.getBusinessId(), startTime, serviceDuration);

        Map<Long /* customer id */, MoeGroomingAppointment> customerIdToAppointment =
                buildAppointmentsForQuickCheckIn(request, customerIdList, startTime, endTime);

        Map<Long /* pet id */, Long /* lodging unit id*/> petLodgingMap =
                getPetAssignLodgingForQuickCheckIn(request, customerIdList, petIdList);

        Map<Long /* customer id */, List<PetDetailDTO>> customerIdToPetDetails = buildPetDetailsForQuickCheckIn(
                request,
                customerIdList,
                petIdToCustomerId.entrySet().stream()
                        .collect(Collectors.groupingBy(
                                Map.Entry::getValue, Collectors.mapping(Map.Entry::getKey, Collectors.toList()))),
                petServiceMap.entrySet().stream()
                        .map(entry -> {
                            var petId = entry.getKey();
                            var serviceMap = entry.getValue();
                            return Map.entry(petId, serviceMap.get(request.getServiceId()));
                        })
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)),
                petLodgingMap,
                startTime,
                endTime);

        var createResults = customerIdList.stream()
                .map(customerId -> CompletableFuture.supplyAsync(
                        () -> {
                            // 创建 appointment，status 为 unconfirmed
                            var appointment = customerIdToAppointment.get(customerId);
                            var result = appointmentCompositeService.createAppointment(
                                    appointment, customerIdToPetDetails.get(customerId), List.of());

                            // Apply pricing rule
                            pricingRuleApplyService.applyPricingRule(
                                    result.getAppointmentId(), request.getCompanyId(), request.getBusinessId());

                            orderRemoteService.createOrder(
                                    appointment,
                                    petDetailService.getPetDetailList(result.getAppointmentId()),
                                    List.of(),
                                    petServiceMap.get(petIdList.get(0)),
                                    false);

                            publisher.publishEvent(new CreateAppointmentEvent(this)
                                    .setCompanyId(request.getCompanyId())
                                    .setBusinessId(request.getBusinessId())
                                    .setStaffId(request.getStaffId())
                                    .setCustomerId(customerId)
                                    .setAppointmentId(result.getAppointmentId())
                                    .setPetDetailDefs(customerIdToPetDetails.get(customerId).stream()
                                            .map(petDetailDTO -> {
                                                var petDetailDef = PetDetailDef.newBuilder()
                                                        .setPetId(petDetailDTO.getPetId())
                                                        .addServices(SelectedServiceDef.newBuilder()
                                                                .setStartDate(DateConverter.INSTANCE
                                                                        .fromGoogleDate(request.getDate())
                                                                        .toString())
                                                                .setEndDate(DateConverter.INSTANCE
                                                                        .fromGoogleDate(request.getDate())
                                                                        .toString())
                                                                .setServiceId(request.getServiceId())
                                                                .setStartTime(startTime)
                                                                .setEndTime(endTime)
                                                                .build());
                                                return petDetailDef.build();
                                            })
                                            .toList())
                                    .setAppointment(appointment));

                            // check in appointment
                            checkIn(appointment);

                            return result.getAppointmentId();
                        },
                        ThreadPool.getSubmitExecutor()))
                .toList();

        CompletableFuture.allOf(createResults.toArray(CompletableFuture[]::new)).join();

        return createResults.stream().map(CompletableFuture::join).collect(Collectors.toList());
    }

    public Integer calculateAppointmentEndTime(Long companyId, Long businessId, Integer startTime, Integer duration) {
        int endTime = startTime + duration;

        // quick checkin 时，end_time 不能超过当天的 working hour
        var workingHourEndTime = getWorkingEndTimeTodayByBusiness(companyId, businessId);

        // 预约开始于 working hour 内，结束时间不能超过 working hour end time
        if (startTime <= workingHourEndTime) {
            endTime = Math.min(endTime, workingHourEndTime);
        } else {
            // 预约开始于 working hour end time 之后，不允许跨天
            endTime = Math.min(endTime, 1439);
        }
        return endTime;
    }

    private Integer getWorkingEndTimeTodayByBusiness(long companyId, long businessId) {
        WorkingHoursDef businessWorkingHours = businessHelper.getBusinessWorkingHours(companyId, businessId);
        String timezoneName = companyRemoteService.getTimezoneName(companyId);
        int dateToWeek = WeekUtil.getDayOfWeek(LocalDate.now(ZoneId.of(timezoneName)));

        return switch (dateToWeek) {
            case WeekUtil.INDEX_OF_MONDAY -> getWorkingHourEndTime(businessWorkingHours.getMondayList());
            case WeekUtil.INDEX_OF_TUESDAY -> getWorkingHourEndTime(businessWorkingHours.getTuesdayList());
            case WeekUtil.INDEX_OF_WEDNESDAY -> getWorkingHourEndTime(businessWorkingHours.getWednesdayList());
            case WeekUtil.INDEX_OF_THURSDAY -> getWorkingHourEndTime(businessWorkingHours.getThursdayList());
            case WeekUtil.INDEX_OF_FRIDAY -> getWorkingHourEndTime(businessWorkingHours.getFridayList());
            case WeekUtil.INDEX_OF_SATURDAY -> getWorkingHourEndTime(businessWorkingHours.getSaturdayList());
            case WeekUtil.INDEX_OF_SUNDAY_MOE -> getWorkingHourEndTime(businessWorkingHours.getSundayList());
            default -> throw bizException(Code.CODE_PARAMS_ERROR, "The appointment time is invalid");
        };
    }

    private Integer getWorkingHourEndTime(List<TimeRangeDef> timeRanges) {
        if (CollectionUtils.isEmpty(timeRanges)) {
            return 1439; // 1440 - 1 当天最后一分钟
        }
        return timeRanges.get(timeRanges.size() - 1).getEndTime();
    }

    private Map<Long, Long> getPetAssignLodgingForQuickCheckIn(
            BatchQuickCheckInRequest request, List<Long> customerIdList, List<Long> petIdList) {

        // get last lodging unit id
        Map<Pair<Long /* pet id */, Long /* service id */>, Long /* lodging unit id */> petLastLodginglMap =
                appointmentCompositeService.getLastLodgingUnitId(
                        request.getCompanyId(),
                        request.getBusinessId(),
                        customerIdList,
                        petIdList,
                        List.of(request.getServiceId()));

        // get pet assign lodging
        Map<Long, Long> petAssignLodgingMap = getPetAssignLodgingMap(request, petIdList);

        Map<Long, Long> result = new HashMap<>();

        // 优先使用 last lodging unit id
        for (Long petId : petIdList) {
            Long lodgingUnitId = Optional.ofNullable(petLastLodginglMap.get(Pair.of(petId, request.getServiceId())))
                    .orElse(petAssignLodgingMap.get(petId));
            result.put(petId, lodgingUnitId);
        }
        return result;
    }

    public Map<Long, Long> getPetAssignLodgingMap(BatchQuickCheckInRequest request, List<Long> petIdList) {
        var companyId = request.getCompanyId();
        var businessId = request.getBusinessId();
        var dateStr = DateConverter.INSTANCE.fromGoogleDate(request.getDate()).toString();
        // 获取 pet、service 信息，用于 lodging available 判断
        Map<Long, BusinessCustomerPetInfoModel> petMap = petRemoteService.getPetMap(companyId, petIdList);

        List<BusinessPetSizeModel> petSizeList = petRemoteService.getPetSizeList(companyId);
        var serviceList =
                offeringRemoteService.getServiceModels(request.getCompanyId(), List.of(request.getServiceId()));
        if (CollectionUtils.isEmpty(serviceList)) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Service not found");
        }
        var serviceBrief = serviceList.get(0);

        // 获取 lodging 信息
        List<LodgingTypeModel> lodgingTypeList = lodgingRemoteService.getLodgingType(companyId);
        List<LodgingUnitModel> lodgingUnitList = lodgingService.listLodgings(companyId);

        // 获取 lodging 使用信息
        List<LodgingAssignInfo> assignInfoList = lodgingService.getAssignInfo(
                companyId,
                businessId,
                dateStr,
                dateStr,
                lodgingUnitList.stream().map(LodgingUnitModel::getId).toList());

        return LodgingService.batchCheckInAutoAssignLodging(BatchCheckInAutoAssignParam.builder()
                .petIds(petIdList)
                .date(dateStr)
                .lodgingTypeList(lodgingTypeList)
                .lodgingUnitList(lodgingUnitList)
                .petMap(petMap)
                .petSizeList(petSizeList)
                .assignInfoList(assignInfoList)
                .serviceBrief(serviceBrief)
                .build());
    }

    private void checkIn(MoeGroomingAppointment appointment) {

        var updateBean = new MoeGroomingAppointment();
        updateBean.setId(appointment.getId());
        updateBean.setStatus((byte) AppointmentStatus.CHECKED_IN_VALUE);
        updateBean.setStatusBeforeCheckin(appointment.getStatus());
        updateBean.setCheckInTime(Instant.now().getEpochSecond());

        appointmentService.update(updateBean);
    }

    @Override
    public void listAppointmentForPets(
            ListAppointmentForPetsRequest request, StreamObserver<ListAppointmentForPetsResponse> responseObserver) {
        var petIdToAppointmentList = appointmentService
                .listAppointmentsForPet(
                        request.getCompanyId(), new HashSet<>(request.getPetIdsList()), request.getFilter())
                .entrySet()
                .stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().stream().map(Pair::first).collect(Collectors.toList())));

        // 获取 Appointment 列表，并且根据主键 ID 去重
        List<AppointmentModel> appointmentModels = petIdToAppointmentList.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toMap(MoeGroomingAppointment::getId, Function.identity(), (a, b) -> a))
                .values()
                .stream()
                .map(AppointmentConverter.INSTANCE::toModel)
                .toList();

        responseObserver.onNext(ListAppointmentForPetsResponse.newBuilder()
                .addAllAppointments(appointmentModels)
                .putAllPetIdToAppointmentIdList(petIdToAppointmentList.entrySet().stream()
                        .collect(Collectors.toMap(Map.Entry::getKey, entry -> Int64ListValue.newBuilder()
                                .addAllValues(entry.getValue().stream()
                                        .map(MoeGroomingAppointment::getId)
                                        .map(Integer::longValue)
                                        .collect(Collectors.toList()))
                                .build())))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void createBlock(CreateBlockRequest request, StreamObserver<CreateBlockResponse> responseObserver) {
        // From POST /grooming/block in moego-server-grooming
        // 1. 构造 appointment
        var appointment = new MoeGroomingAppointment();
        var startTime = Instant.ofEpochSecond(
                        request.getStartTime().getSeconds(),
                        request.getStartTime().getNanos())
                .atZone(ZoneOffset.UTC);
        var endTime = Instant.ofEpochSecond(
                        request.getEndTime().getSeconds(), request.getEndTime().getNanos())
                .atZone(ZoneOffset.UTC)
                .toLocalDateTime();
        appointment.setBusinessId((int) request.getBusinessId());
        appointment.setCompanyId(request.getCompanyId());
        appointment.setCustomerId(0);
        appointment.setColorCode(request.getColorCode());
        appointment.setAppointmentDate(startTime.toLocalDate().toString());
        appointment.setAppointmentEndDate(endTime.toLocalDate().toString());
        appointment.setAppointmentStartTime(startTime.toLocalTime().toSecondOfDay() / 60);
        appointment.setAppointmentEndTime(endTime.toLocalTime().toSecondOfDay() / 60);
        appointment.setIsBlock((byte) 1);
        appointment.setStatus((byte) AppointmentStatus.UNCONFIRMED_VALUE);
        appointment.setSource(request.getSourceValue());
        appointment.setCreatedById((int) request.getCreatedBy());
        appointment.setUpdatedById(request.getCreatedBy());
        appointment.setCreateTime(CommonUtil.get10Timestamp());
        appointment.setUpdateTime(CommonUtil.get10Timestamp());
        appointment.setServiceTypeInclude(ServiceItemEnum.GROOMING.getBitValue());

        // 2. 构造 pet detail
        var petDetail = new MoeGroomingPetDetail();
        petDetail.setStartDate(appointment.getAppointmentDate());
        petDetail.setEndDate(appointment.getAppointmentDate());
        petDetail.setStaffId((int) request.getStaffId());
        petDetail.setStartTime((long) appointment.getAppointmentStartTime());
        petDetail.setEndTime((long) appointment.getAppointmentEndTime());
        petDetail.setServiceTime(appointment.getAppointmentEndTime() - appointment.getAppointmentStartTime());
        petDetail.setUpdateTime(appointment.getUpdateTime());
        petDetail.setServiceItemType((byte) ServiceItemType.GROOMING_VALUE);

        // 3. 创建 block
        MoeGroomingAppointment block = appointmentCompositeService.createBlock(appointment, petDetail);

        // 4. 创建 block note
        if (Strings.isNullOrEmpty(request.getDescription())) {
            var note = new MoeGroomingNote();
            note.setCompanyId(block.getCompanyId());
            note.setBusinessId(block.getBusinessId());
            note.setCustomerId(block.getCustomerId());
            note.setGroomingId(block.getId());
            note.setCreateBy(block.getCreatedById());
            note.setUpdateBy(block.getUpdatedById().intValue());
            note.setCreateTime(block.getCreateTime());
            note.setUpdateTime(block.getUpdateTime());
            noteService.insertMultiple(List.of(note));
        }

        // 5. 发布创建 block 的事件
        publisher.publishEvent(new CreateBlockEvent(this)
                .setCompanyId(block.getCompanyId())
                .setBusinessId(block.getBusinessId().longValue())
                .setStaffId(request.getStaffId())
                .setCustomerId(Long.valueOf(block.getCustomerId()))
                .setAppointmentId(Long.valueOf(block.getId())));

        // 6. 返回结果
        responseObserver.onNext(
                CreateBlockResponse.newBuilder().setId(block.getId()).build());
        responseObserver.onCompleted();
    }

    @Override
    public void listAppointments(
            ListAppointmentsRequest request, StreamObserver<ListAppointmentsResponse> responseObserver) {
        var pageResult = appointmentService.listAppointments(
                request.getCompanyId(),
                request.getBusinessIdsList(),
                request.getFilter(),
                request.getOrderBysList(),
                PageConverter.INSTANCE.toPageInfo(request.getPagination()),
                request.getPriorityOrderType());
        var appointments = AppointmentConverter.INSTANCE.toModel(pageResult.first());
        if (featureFlagHelper.isNewOrderFlow(request.getCompanyId())) {
            var appointmentIds = pageResult.first().stream()
                    .map(MoeGroomingAppointment::getId)
                    .map(Integer::longValue)
                    .toList();
            var appointmentIdToNewOrder = appointmentExtraInfoService.listNewOrderFlag(appointmentIds);
            appointments = appointments.stream()
                    .map(appointment -> appointment.toBuilder()
                            .setIsNewOrder(appointmentIdToNewOrder.getOrDefault(appointment.getId(), false))
                            .build())
                    .toList();
        }
        responseObserver.onNext(ListAppointmentsResponse.newBuilder()
                .addAllAppointments(appointments)
                .setPagination(PageConverter.INSTANCE.toResponse(pageResult.second()))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void listBlockTimes(ListBlockTimesRequest request, StreamObserver<ListBlockTimesResponse> responseObserver) {
        List<BlockTimeModel> blockTimeModels = appointmentService.listBlockTimes(
                request.getCompanyId(), request.getBusinessIdsList(), request.getFilter());
        responseObserver.onNext(ListBlockTimesResponse.newBuilder()
                .addAllBlockTimes(blockTimeModels)
                .build());
        responseObserver.onCompleted();
    }

    private static Map<Long /* customer id */, MoeGroomingAppointment> buildAppointmentsForQuickCheckIn(
            BatchQuickCheckInRequest request, List<Long> customerIds, Integer startTime, Integer endTime) {
        Map<Long, MoeGroomingAppointment> result = new HashMap<>();
        customerIds.forEach(customerId -> {
            MoeGroomingAppointment appointment = new MoeGroomingAppointment();
            appointment.setOrderId(CommonUtil.getUuid());
            appointment.setBusinessId((int) request.getBusinessId());
            appointment.setCompanyId(request.getCompanyId());
            appointment.setCustomerId(customerId.intValue());
            appointment.setStatus((byte) AppointmentStatus.UNCONFIRMED_VALUE);
            appointment.setIsPaid((byte) AppointmentPaymentStatus.UNPAID_VALUE);
            appointment.setCreatedById((int) request.getStaffId());
            appointment.setUpdatedById(request.getStaffId());
            appointment.setCreateTime(CommonUtil.get10Timestamp());
            appointment.setUpdateTime(CommonUtil.get10Timestamp());
            appointment.setAppointmentDate(
                    DateConverter.INSTANCE.fromGoogleDate(request.getDate()).toString());
            appointment.setAppointmentEndDate(
                    DateConverter.INSTANCE.fromGoogleDate(request.getDate()).toString());
            appointment.setAppointmentStartTime(startTime);
            appointment.setAppointmentEndTime(endTime);
            appointment.setServiceTypeInclude(ServiceItemEnum.DAYCARE.getBitValue());
            appointment.setSource(request.getSourceValue());
            result.put(customerId, appointment);
        });

        return result;
    }

    private static Map<Long /* customer id */, List<PetDetailDTO>> buildPetDetailsForQuickCheckIn(
            BatchQuickCheckInRequest request,
            List<Long> customerIds,
            Map<Long /* customer id */, List<Long> /* pet id list */> customerPetMap,
            Map<Long /* pet id */, CustomizedServiceView> petServiceMap,
            Map<Long /* pet id */, Long /* lodging unit id */> petLodgingMap,
            Integer startTime,
            Integer endTime) {
        Map<Long, List<PetDetailDTO>> result = new HashMap<>();
        customerIds.forEach(customerId -> {
            List<PetDetailDTO> petDetails = customerPetMap.get(customerId).stream()
                    .map(petId -> {
                        CustomizedServiceView service = Optional.ofNullable(petServiceMap.get(petId))
                                .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Pet service not found"));
                        MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
                        petDetail.setPetId(petId.intValue());
                        petDetail.setServiceId(Math.toIntExact(request.getServiceId()));
                        petDetail.setServiceType(ServiceType.SERVICE_VALUE);
                        petDetail.setServiceItemType((byte) ServiceItemType.DAYCARE_VALUE);
                        petDetail.setPriceUnit(service.getPriceUnitValue());
                        petDetail.setUpdateTime(DateUtil.get10Timestamp());
                        petDetail.setServicePrice(BigDecimal.valueOf(service.getPrice()));
                        // 默认值
                        petDetail.setScopeTypePrice(ServiceScopeType.DO_NOT_SAVE_VALUE);
                        petDetail.setScopeTypeTime(ServiceScopeType.DO_NOT_SAVE_VALUE);
                        petDetail.setStartDate(DateConverter.INSTANCE
                                .fromGoogleDate(request.getDate())
                                .toString());
                        petDetail.setEndDate(DateConverter.INSTANCE
                                .fromGoogleDate(request.getDate())
                                .toString());
                        petDetail.setStartTime(startTime.longValue());
                        petDetail.setEndTime(endTime.longValue());
                        petDetail.setServiceTime(service.getMaxDuration());

                        if (CommonUtil.isNormal(petLodgingMap.get(petId))) {
                            petDetail.setLodgingId(petLodgingMap.get(petId));
                        }

                        return new PetDetailDTO()
                                .setPetId(petId)
                                .setPetDetail(petDetail)
                                .setService(service);
                    })
                    .collect(Collectors.toList());
            result.put(customerId, petDetails);
        });
        return result;
    }

    private Integer getNowMinuteForCompany(Long companyId) {
        var timeZone = organizationRemoteService.getTimezone(companyId);
        return DateUtil.getNowMinutes(timeZone);
    }

    @Override
    public void listAppointmentsForCustomers(
            ListAppointmentsForCustomersRequest request,
            StreamObserver<ListAppointmentsForCustomersResponse> responseObserver) {
        var appointments = appointmentService.listAppointmentsForCustomer(
                request.getCompanyId(), request.getCustomerIdsList(), request.getFilter());

        responseObserver.onNext(ListAppointmentsForCustomersResponse.newBuilder()
                .addAllAppointments(AppointmentConverter.INSTANCE.toModel(appointments))
                .build());
        responseObserver.onCompleted();
    }

    private static boolean isApptFinishOrCancelled(Byte apptStatus) {
        return (Objects.equals(apptStatus, AppointmentStatusEnum.FINISHED.getValue())
                || Objects.equals(apptStatus, AppointmentStatusEnum.CANCELED.getValue()));
    }

    @Override
    public void cancelAppointment(
            CancelAppointmentRequest request, StreamObserver<CancelAppointmentResponse> responseObserver) {
        var appointment = appointmentService.mustGet(request.getAppointmentId());
        if (isApptFinishOrCancelled(appointment.getStatus())) {
            responseObserver.onNext(CancelAppointmentResponse.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        // Cancel appointment and create cancel reason note
        appointmentCompositeService.cancelAppointment(toCancelAppointmentDTO(request));

        // Cancel repeat appointments
        ThreadPool.execute(() -> repeatAppointmentService
                .listRepeatAppointment(request.getAppointmentId(), request.getRepeatAppointmentModifyScope())
                .forEach(repeatAppointment -> {
                    var cancelAppointmentDTO = toCancelAppointmentDTO(request)
                            .setAppointmentId(repeatAppointment.getId().longValue())
                            .setRepeat(true);
                    appointmentCompositeService.cancelAppointment(cancelAppointmentDTO);

                    activityLogService.createActivityLog(CreateActivityLogRequest.newBuilder()
                            .setCompanyId(repeatAppointment.getCompanyId())
                            .setBusinessId(repeatAppointment.getBusinessId())
                            .setOperatorId(String.valueOf(request.getCancelBy()))
                            .setAction(AppointmentAction.CANCEL)
                            .setResourceType(Resource.Type.APPOINTMENT)
                            .setResourceId(String.valueOf(repeatAppointment.getId()))
                            .setDetails(JsonUtil.toJson(
                                    new CancelLogDTO(request.getCancelReason(), request.getCancelByType())))
                            .build());
                }));

        responseObserver.onNext(CancelAppointmentResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    private static CancelAppointmentDTO toCancelAppointmentDTO(CancelAppointmentRequest request) {
        return new CancelAppointmentDTO()
                .setAppointmentId(request.getAppointmentId())
                .setCancelByType(request.getCancelByType())
                .setCancelBy(request.getCancelBy())
                .setCancelReason(request.hasCancelReason() ? request.getCancelReason() : null)
                .setNoShow(request.getNoShow());
    }

    @Override
    public void batchBookAgainAppointment(
            BatchBookAgainAppointmentRequest request,
            StreamObserver<BatchBookAgainAppointmentResponse> responseObserver) {

        List<MoeGroomingAppointment> appointments =
                appointmentService.getAppointments(request.getCompanyId(), request.getAppointmentIdsList());
        Map<Long, List<MoeGroomingPetDetail>> petDetailsByAppointmentIds =
                petDetailService.getPetDetailsByAppointmentIds(request.getAppointmentIdsList());
        Map<Long, List<MoeGroomingServiceOperation>> serviceOperationMapByPetDetailIds =
                serviceOperationService.getServiceOperationMapByPetDetailIds(request.getAppointmentIdsList());

        Map<Long, List<PetDetailDTO>> appointmentIdToPetDetails = buildRebookPetDetailsForAppointment(
                appointments, petDetailsByAppointmentIds, serviceOperationMapByPetDetailIds);

        List<MoeGroomingAppointment> newAppointments =
                setRebookAppointmentPetDetailsDefault(request, appointments, appointmentIdToPetDetails);

        newAppointments.forEach(appointment -> {
            var oldAppointmentId = appointment.getId().longValue();
            appointment.setId(null);
            var petDetailDtoList = appointmentIdToPetDetails.get(oldAppointmentId);
            var moeGroomingPetDetails = petDetailsByAppointmentIds.get(oldAppointmentId);
            var result = appointmentCompositeService.createAppointment(appointment, petDetailDtoList, List.of());

            pricingRuleApplyService.applyPricingRule(
                    result.getAppointmentId(),
                    appointment.getCompanyId(),
                    appointment.getBusinessId().longValue());

            Map<Long, CustomizedServiceView> serviceModelMap = petDetailDtoList.stream()
                    .map(PetDetailDTO::getService)
                    .collect(Collectors.toMap(CustomizedServiceView::getId, Function.identity(), (s1, s2) -> s1));
            orderRemoteService.createOrder(appointment, moeGroomingPetDetails, List.of(), serviceModelMap, false);

            publisher.publishEvent(new CreateAppointmentEvent(this)
                    .setCompanyId(appointment.getCompanyId())
                    .setBusinessId(appointment.getBusinessId().longValue())
                    .setStaffId(appointment.getCreatedById().longValue())
                    .setCustomerId(appointment.getCustomerId().longValue())
                    .setAppointmentId(result.getAppointmentId())
                    .setPetDetailDefs(petDetailDtoList.stream()
                            .map(petDetailDTO -> {
                                var petDetailDef = PetDetailDef.newBuilder()
                                        .setPetId(petDetailDTO.getPetId())
                                        .addServices(SelectedServiceDef.newBuilder()
                                                .setStartDate(appointment.getAppointmentDate())
                                                .setEndDate(appointment.getAppointmentDate())
                                                .setServiceId(petDetailDTO
                                                        .getService()
                                                        .getId())
                                                .setStartTime(appointment.getAppointmentStartTime())
                                                .setEndTime(appointment.getAppointmentEndTime())
                                                .build());
                                return petDetailDef.build();
                            })
                            .toList())
                    .setAppointment(appointment));
        });

        responseObserver.onNext(BatchBookAgainAppointmentResponse.newBuilder()
                .addAllAppointments(AppointmentConverter.INSTANCE.toModel(newAppointments))
                .build());
        responseObserver.onCompleted();
    }

    public Map<Long /* appointment id */, List<PetDetailDTO>> buildRebookPetDetailsForAppointment(
            List<MoeGroomingAppointment> appointments,
            Map<Long, List<MoeGroomingPetDetail>> petDetailsByAppointmentIds,
            Map<Long, List<MoeGroomingServiceOperation>> serviceOperationMapByPetDetailIds) {

        Map<Long, List<PetDetailDTO>> result = new HashMap<>();
        appointments.forEach(appointment -> {
            var petDetails = petDetailsByAppointmentIds.get(appointment.getId().longValue());

            Map<Long, List<Long>> petServiceIdsMap = petDetails.stream()
                    .collect(Collectors.groupingBy(
                            p -> Long.valueOf(p.getPetId()),
                            Collectors.mapping(p -> Long.valueOf(p.getServiceId()), Collectors.toList())));

            Map<Long, Map<Long, CustomizedServiceView>> petServiceMap = offeringRemoteService.listService(
                    appointment.getCompanyId(), appointment.getBusinessId().longValue(), petServiceIdsMap);

            result.put(
                    appointment.getId().longValue(),
                    petDetails.stream()
                            .map(petDetail -> new PetDetailDTO()
                                    .setPetDetail(petDetail)
                                    .setOperations(serviceOperationMapByPetDetailIds.getOrDefault(
                                            petDetail.getId().longValue(), List.of()))
                                    .setService(petServiceMap
                                            .get(petDetail.getPetId().longValue())
                                            .get(petDetail.getServiceId().longValue()))
                                    .setPetId(petDetail.getPetId().longValue()))
                            .toList());
        });
        return result;
    }

    public List<MoeGroomingAppointment> setRebookAppointmentPetDetailsDefault(
            BatchBookAgainAppointmentRequest request,
            List<MoeGroomingAppointment> appointments,
            Map<Long, List<PetDetailDTO>> appointmentIdToPetDetails) {
        var targetDate =
                DateConverter.INSTANCE.fromGoogleDate(request.getTargetDate()).toString();
        var rebookBy = request.getRebookBy();
        var newAppointmentList = new ArrayList<MoeGroomingAppointment>();

        appointments.forEach(appointment -> {
            var oldAppointmentId = appointment.getId();
            var petDetailDtoList = appointmentIdToPetDetails.get(oldAppointmentId.longValue());

            // Calculate appointment end date
            int duration = PetDetailUtil.calculateAppointmentTime(appointment);
            Pair<String, Integer> endDateAndEndTime = PetDetailUtil.calculateEndDateAndEndTime(
                    targetDate, appointment.getAppointmentStartTime(), duration);

            petDetailDtoList.forEach(petDetailDTO -> {
                petDetailDTO.getOperations().forEach(serviceOperation -> {
                    serviceOperation.setId(null);
                    serviceOperation.setGroomingId(null);
                    serviceOperation.setGroomingServiceId(null);
                });

                Pair<String, Integer> endDateAndEndTimeForPetDetail = PetDetailUtil.calculateEndDateAndEndTime(
                        targetDate, petDetailDTO.getPetDetail().getStartTime().intValue(), duration);

                petDetailDTO.getPetDetail().setId(null);
                petDetailDTO.getPetDetail().setGroomingId(null);
                petDetailDTO.getPetDetail().setStartDate(targetDate);
                petDetailDTO.getPetDetail().setEndDate(endDateAndEndTimeForPetDetail.first());
                petDetailDTO.getPetDetail().setUpdateTime(CommonUtil.get10Timestamp());
            });

            MoeGroomingAppointment newAppointment = new MoeGroomingAppointment();
            newAppointment.setId(oldAppointmentId); // Reuse old appointment id for rebook, cancel it later
            newAppointment.setCompanyId(appointment.getCompanyId());
            newAppointment.setBusinessId(appointment.getBusinessId());
            newAppointment.setAppointmentDate(targetDate);
            newAppointment.setAppointmentEndDate(endDateAndEndTime.first());
            newAppointment.setStatus((byte) AppointmentStatus.UNCONFIRMED_VALUE);
            newAppointment.setIsPaid((byte) AppointmentPaymentStatus.UNPAID_VALUE);
            newAppointment.setOrderId(CommonUtil.getUuid());
            newAppointment.setCreatedById((int) rebookBy);
            newAppointment.setUpdatedById(rebookBy);
            newAppointment.setCreateTime(CommonUtil.get10Timestamp());
            newAppointment.setUpdateTime(CommonUtil.get10Timestamp());
            newAppointment.setAppointmentStartTime(appointment.getAppointmentStartTime());
            newAppointment.setAppointmentEndTime(endDateAndEndTime.second());
            newAppointment.setServiceTypeInclude(ServiceItemEnum.GROOMING.getBitValue());
            newAppointment.setSource(appointment.getSource());
            newAppointment.setCustomerId(appointment.getCustomerId());
            newAppointment.setIsBlock(appointment.getIsBlock());
            newAppointment.setCustomerAddressId(appointment.getCustomerAddressId());
            newAppointment.setColorCode(appointment.getColorCode());

            newAppointmentList.add(newAppointment);
        });
        return newAppointmentList;
    }

    @Override
    public void batchCancelAppointment(
            BatchCancelAppointmentRequest request, StreamObserver<BatchCancelAppointmentResponse> responseObserver) {

        List<MoeGroomingAppointment> appointments =
                appointmentService.getAppointments(request.getCompanyId(), request.getAppointmentIdsList());

        appointments.forEach(appointment -> {
            if (isApptFinishOrCancelled(appointment.getStatus())) {
                return;
            }
            CancelAppointmentDTO cancelAppointmentDTO = new CancelAppointmentDTO()
                    .setAppointmentId(appointment.getId().longValue())
                    .setCancelBy(request.getCancelBy())
                    .setCancelReason(request.hasCancelReason() ? request.getCancelReason() : null)
                    .setCancelByType(AppointmentUpdatedBy.BY_BUSINESS)
                    .setNoShow(AppointmentNoShowStatus.NOT_NO_SHOW);

            // Cancel appointment and create cancel reason note
            appointmentCompositeService.cancelAppointment(cancelAppointmentDTO);
        });

        responseObserver.onNext(BatchCancelAppointmentResponse.newBuilder()
                .addAllAppointments(AppointmentConverter.INSTANCE.toModel(appointments))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void countAppointmentForPets(
            CountAppointmentForPetsRequest request, StreamObserver<CountAppointmentForPetsResponse> responseObserver) {
        if (request.getPetIdsCount() == 0) {
            responseObserver.onNext(CountAppointmentForPetsResponse.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        var petIdToAppointmentCount = appointmentService.countAppointmentForPets(request.getPetIdsList());

        responseObserver.onNext(CountAppointmentForPetsResponse.newBuilder()
                .putAllPetIdToAppointmentCount(petIdToAppointmentCount)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void deleteAppointments(
            DeleteAppointmentsRequest request, StreamObserver<DeleteAppointmentsResponse> responseObserver) {

        var affectedRows = Tx.doInTransaction(() -> appointmentService.deleteAppointments(
                request.hasCompanyId() ? request.getCompanyId() : null,
                request.hasBusinessId() ? Math.toIntExact(request.getBusinessId()) : null,
                request.getIdsList().stream().map(Long::intValue).collect(Collectors.toSet())));

        responseObserver.onNext(DeleteAppointmentsResponse.newBuilder()
                .setAffectedRows(affectedRows)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void restoreAppointments(
            RestoreAppointmentsRequest request, StreamObserver<RestoreAppointmentsResponse> responseObserver) {

        int affectedRows = Tx.doInTransaction(() -> appointmentService.restoreAppointments(
                request.hasCompanyId() ? request.getCompanyId() : null,
                request.hasBusinessId() ? Math.toIntExact(request.getBusinessId()) : null,
                request.getIdsList().stream().map(Long::intValue).collect(Collectors.toSet())));

        responseObserver.onNext(RestoreAppointmentsResponse.newBuilder()
                .setAffectedRows(affectedRows)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void rescheduleBoardingAppointment(
            RescheduleBoardingAppointmentRequest request,
            StreamObserver<RescheduleBoardingAppointmentResponse> responseObserver) {

        verifyAppointmentExists(request.hasCompanyId() ? request.getCompanyId() : null, request.getAppointmentId());

        var petDetails = petDetailService.getPetDetailList(request.getAppointmentId());

        var boardingServicePetDetails = getPetDetailsForBoardingService(petDetails);
        if (boardingServicePetDetails.isEmpty()) {
            responseObserver.onNext(RescheduleBoardingAppointmentResponse.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        // 一个 appointment 里面所有 boarding service 的 start date 是相同的，end date 也是相同的，
        // 这个条件是必要的，不然这个接口不能这样设计。
        var boarding = boardingServicePetDetails.get(0);
        var newStartDate = request.hasStartDate() ? request.getStartDate() : boarding.getStartDate();
        var newEndDate = request.hasEndDate() ? request.getEndDate() : boarding.getEndDate();

        // 实际 checkout date <= start_date，不处理这种请求
        if (newEndDate.compareTo(newStartDate) <= 0) {
            responseObserver.onNext(RescheduleBoardingAppointmentResponse.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        for (var boardingServicePetDetail : boardingServicePetDetails) {
            var pd = new MoeGroomingPetDetail();
            pd.setId(boardingServicePetDetail.getId());
            pd.setStartDate(newStartDate);
            pd.setEndDate(newEndDate);
            petDetailService.update(pd, false);
        }

        var appointment = appointmentService.getAppointment(
                request.hasCompanyId() ? request.getCompanyId() : null, request.getAppointmentId());

        pricingRuleApplyService.applyPricingRule(
                appointment.getId().longValue(),
                appointment.getCompanyId(),
                appointment.getBusinessId().longValue());

        orderRemoteService.updateOrder(appointment);

        responseObserver.onNext(RescheduleBoardingAppointmentResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    private void verifyAppointmentExists(@Nullable Long companyId, long appointmentId) {
        appointmentService.getAppointment(companyId, appointmentId);
    }

    private static List<MoeGroomingPetDetail> getPetDetailsForBoardingService(List<MoeGroomingPetDetail> petDetails) {
        return petDetails.stream()
                .filter(e -> Objects.equals(e.getServiceItemType(), (byte) ServiceItemType.BOARDING_VALUE))
                .filter(e -> Objects.equals(e.getServiceType(), ServiceType.SERVICE_VALUE))
                .toList();
    }

    @Override
    public void syncAppointmentToOrder(
            SyncAppointmentToOrderRequest request, StreamObserver<SyncAppointmentToOrderResponse> responseObserver) {
        var appointment = appointmentService.getAppointment(request.getAppointmentId());
        if (appointment != null
                && !Objects.equals(appointment.getStatus(), (byte) AppointmentStatus.CANCELED.getNumber())) {
            orderRemoteService.updateOrder(appointment);
        }

        responseObserver.onNext(SyncAppointmentToOrderResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void previewOrderDetail(
            PreviewOrderDetailRequest request, StreamObserver<PreviewOrderDetailResponse> responseObserver) {
        var appointments = appointmentService.getAppointments(null, request.getAppointmentIdsList());
        var petDetails = petDetailService.getPetDetailList(request.getAppointmentIdsList());
        var evaluationDetails = evaluationService.getPetEvaluationList(request.getAppointmentIdsList());
        var orderDetails = orderRemoteService.previewOrder(appointments, petDetails, evaluationDetails);

        responseObserver.onNext(PreviewOrderDetailResponse.newBuilder()
                .addAllOrderDetails(orderDetails)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void getTimeOverlapAppointmentList(
            GetTimeOverlapAppointmentListRequest request,
            StreamObserver<GetTimeOverlapAppointmentListResponse> responseObserver) {
        Map<Long, List<AppointmentModel>> petIdToAppointmentList = appointmentService.getTimeOverlapAppointmentList(
                request.getCompanyId(), request.getCustomerIdList(), request.getPetIdsList(), request.getFilter());

        GetTimeOverlapAppointmentListResponse.Builder responseBuilder =
                GetTimeOverlapAppointmentListResponse.newBuilder();

        petIdToAppointmentList.forEach((petId, appointmentList) -> {
            responseBuilder.putAppointmentList(
                    petId,
                    AppointmentList.newBuilder()
                            .addAllAppointments(appointmentList)
                            .build());
        });

        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void listExtraInfo(ListExtraInfoRequest request, StreamObserver<ListExtraInfoResponse> responseObserver) {
        var extraInfos = appointmentExtraInfoService.listExtraInfos(request.getAppointmentIdsList());

        responseObserver.onNext(ListExtraInfoResponse.newBuilder()
                .addAllExtraInfo(extraInfos.stream()
                        .map(e -> AppointmentExtraInfoModel.newBuilder()
                                .setId(e.getId())
                                .setAppointmentId(e.getAppointmentId())
                                .setIsNewOrder(e.getIsNewOrder())
                                .build())
                        .toList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void createExtraInfo(
            CreateExtraInfoRequest request, StreamObserver<CreateExtraInfoResponse> responseObserver) {
        var extraInfo = appointmentExtraInfoService.insertNewOrderFlag(request.getAppointmentId());

        responseObserver.onNext(CreateExtraInfoResponse.newBuilder()
                .setExtraInfo(AppointmentExtraInfoModel.newBuilder()
                        .setAppointmentId(extraInfo.getAppointmentId())
                        .setIsNewOrder(extraInfo.getIsNewOrder())
                        .build())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void previewOrderLineItems(
            PreviewOrderLineItemsRequest request, StreamObserver<PreviewOrderLineItemsResponse> responseObserver) {
        var appointmentFuture = supplyAsync(
                () -> appointmentService.mustGet(request.getAppointmentId()), ThreadPool.getSubmitExecutor());
        var petDetailsFuture = supplyAsync(
                () -> petDetailService.getPetDetailList(request.getAppointmentId()), ThreadPool.getSubmitExecutor());
        var evaluationDetailsFuture = supplyAsync(
                () -> evaluationService.getPetEvaluationList(request.getAppointmentId()),
                ThreadPool.getSubmitExecutor());
        var serviceChargeDetailsFuture = supplyAsync(
                () -> serviceChargeDetailService.listByAppointmentId(request.getAppointmentId()),
                ThreadPool.getSubmitExecutor());
        var serviceChargeFuture = appointmentFuture.thenCombineAsync(
                serviceChargeDetailsFuture,
                (appointment, serviceChargeDetails) -> {
                    var serviceChargeIds = serviceChargeDetails.stream()
                            .map(ServiceChargeDetail::getServiceChargeId)
                            .distinct()
                            .toList();
                    return serviceChargeHelper.listByIds(
                            appointment.getCompanyId(), appointment.getBusinessId(), serviceChargeIds);
                },
                ThreadPool.getSubmitExecutor());
        var taxFuture = serviceChargeDetailsFuture.thenApplyAsync(
                organizationRemoteService::listTaxRulesMapByServiceChargeDetails, ThreadPool.getSubmitExecutor());

        var petFuture = petDetailsFuture.thenCombineAsync(
                evaluationDetailsFuture,
                (petDetails, evaluationDetails) -> {
                    var petIds = Stream.of(
                                    petDetails.stream()
                                            .map(MoeGroomingPetDetail::getPetId)
                                            .map(Integer::longValue),
                                    evaluationDetails.stream().map(EvaluationServiceDetail::getPetId))
                            .flatMap(Function.identity())
                            .distinct()
                            .toList();
                    return petRemoteService.getPetMap(appointmentFuture.join().getCompanyId(), petIds);
                },
                ThreadPool.getSubmitExecutor());
        // Wait for all futures to complete and get results
        var appointment = appointmentFuture.join();
        var petDetails = petDetailsFuture.join();
        var evaluationDetails = evaluationDetailsFuture.join();
        var serviceChargeDetails = serviceChargeDetailsFuture.join();
        var idToServiceCharge = serviceChargeFuture.join();
        var petIdToInfo = petFuture.join();
        var taxIdToTax = taxFuture.join();

        var petIdToServiceItems = Stream.of(
                        processPetDetails(appointment, petDetails),
                        processEvaluationDetails(appointment.getCompanyId(), evaluationDetails))
                .flatMap(map -> map.entrySet().stream())
                .collect(Collectors.groupingBy(
                        Map.Entry::getKey,
                        Collectors.flatMapping(entry -> entry.getValue().stream(), Collectors.toList())));

        responseObserver.onNext(PreviewOrderLineItemsResponse.newBuilder()
                .addAllPetServices(LineItemUtils.buildPetServices(petIdToServiceItems, petIdToInfo))
                .addAllSurcharges(LineItemUtils.buildSurcharges(serviceChargeDetails, idToServiceCharge, taxIdToTax))
                .build());
        responseObserver.onCompleted();
    }

    private Map<Long, List<ServiceItem>> processEvaluationDetails(
            long companyId, List<EvaluationServiceDetail> evaluationDetails) {
        var staffFuture = supplyAsync(
                () -> organizationRemoteService.listStaffsByEvaluationDetails(evaluationDetails),
                ThreadPool.getSubmitExecutor());

        var lodgingFuture = supplyAsync(
                () -> lodgingRemoteService.getLodgingMapByEvaluationDetails(companyId, evaluationDetails),
                ThreadPool.getSubmitExecutor());

        var lodgingTypeFuture =
                supplyAsync(() -> lodgingRemoteService.getLodgingTypeMap(companyId), ThreadPool.getSubmitExecutor());

        var evaluationFuture =
                supplyAsync(() -> offeringRemoteService.listEvaluations(companyId), ThreadPool.getSubmitExecutor());

        var taxFuture = evaluationFuture.thenApplyAsync(
                evaluations -> organizationRemoteService.listTaxRulesByEvaluations(evaluations.values()),
                ThreadPool.getSubmitExecutor());

        var idToStaff = staffFuture.join();
        var idToLodging = lodgingFuture.join();
        var idToLodgingType = lodgingTypeFuture.join();
        var idToEvaluation = evaluationFuture.join();
        var idToTax = taxFuture.join();

        return evaluationDetails.stream()
                .collect(Collectors.groupingBy(
                        EvaluationServiceDetail::getPetId,
                        Collectors.mapping(
                                evaluationDetail -> {
                                    var staff = evaluationDetail.getStaffId() != null
                                            ? idToStaff.get(evaluationDetail.getStaffId())
                                            : null;
                                    var lodging = idToLodging.get(evaluationDetail.getLodgingId());
                                    var evaluation = idToEvaluation.getOrDefault(
                                            evaluationDetail.getServiceId(), EvaluationModel.getDefaultInstance());
                                    var tax = idToTax.getOrDefault(
                                            evaluation.getTaxId(), TaxRuleModel.getDefaultInstance());
                                    return convertEvaluationDetailToServiceItem(
                                            evaluationDetail, staff, lodging, idToLodgingType, evaluation, tax);
                                },
                                Collectors.toList())));
    }

    private static ServiceItem convertEvaluationDetailToServiceItem(
            EvaluationServiceDetail evaluationDetail,
            @Nullable StaffModel staff,
            @Nullable LodgingUnitModel lodging,
            Map<Long, LodgingTypeModel> idToLodgingType,
            EvaluationModel evaluation,
            TaxRuleModel tax) {

        return ServiceItem.newBuilder()
                .setServiceId(evaluationDetail.getServiceId())
                .setName(evaluation.getName())
                .setStaffId(getStaffId(evaluationDetail))
                .setDescription(evaluation.getDescription())
                .setServiceItemType(ServiceItemType.EVALUATION)
                .setServiceType(ServiceType.SERVICE)
                .setDetailId(evaluationDetail.getId().intValue())
                .setUnitPrice(MoneyUtils.toGoogleMoney(evaluationDetail.getServicePrice()))
                .setQuantity(1)
                .setTotalPrice(MoneyUtils.toGoogleMoney(evaluationDetail.getServicePrice()))
                .addAllLineItems(LineItemUtils.buildLineItems(evaluationDetail, staff, lodging, idToLodgingType))
                .setTax(tax)
                .setExternalId(LineItemUtils.EVALUATION_EXTERNAL_ID_PREFIX
                        + evaluationDetail.getId().intValue())
                .build();
    }

    static long getStaffId(EvaluationServiceDetail evaluationDetail) {
        return evaluationDetail.getStaffId() != null ? evaluationDetail.getStaffId() : 0;
    }

    private Map<Long, List<ServiceItem>> processPetDetails(
            MoeGroomingAppointment appointment, List<MoeGroomingPetDetail> petDetails) {
        var companyId = appointment.getCompanyId();
        var businessId = appointment.getBusinessId();
        var appointmentId = appointment.getId().longValue();

        // Create CompletableFutures for each independent service call
        var staffFuture = supplyAsync(
                () -> organizationRemoteService.listStaffsByPetDetails(petDetails), ThreadPool.getSubmitExecutor());

        var serviceMapFuture = supplyAsync(
                () -> offeringRemoteService.getCustomizedServiceMap(petDetails, companyId, businessId),
                ThreadPool.getSubmitExecutor());

        var splitLodgingsFuture = supplyAsync(
                () -> boardingSplitLodgingService.getBoardingSplitLodgings(List.of(appointmentId)),
                ThreadPool.getSubmitExecutor());

        var lodgingMapFuture = splitLodgingsFuture.thenApplyAsync(
                splitLodgingMap -> lodgingRemoteService.getLodgingMap(petDetails, splitLodgingMap, companyId),
                ThreadPool.getSubmitExecutor());

        var lodgingTypeFuture =
                supplyAsync(() -> lodgingRemoteService.getLodgingTypeMap(companyId), ThreadPool.getSubmitExecutor());

        var pricingRuleApplyLogsFuture = supplyAsync(
                () -> pricingRuleRecordApplyService.getApplyLogByAppointmentId(companyId, appointmentId),
                ThreadPool.getSubmitExecutor());

        var timeZoneNameFuture =
                supplyAsync(() -> organizationRemoteService.getTimezone(companyId), ThreadPool.getSubmitExecutor());

        var taxMapFuture = serviceMapFuture.thenApplyAsync(
                serviceMap -> organizationRemoteService.listTaxRulesMapByServices(serviceMap.values()),
                ThreadPool.getSubmitExecutor());

        var staffMap = staffFuture.join();
        var idToService = serviceMapFuture.join();
        var idToTax = taxMapFuture.join();
        var idToLodging = lodgingMapFuture.join();
        var idToLodgingType = lodgingTypeFuture.join();
        var petDetailIdToSplitLodgings = splitLodgingsFuture.join().stream()
                .collect(Collectors.groupingBy(BoardingSplitLodging::getPetDetailId));
        var petServiceToPricingRuleLogs = pricingRuleApplyLogsFuture.join().stream()
                .collect(Collectors.groupingBy(p -> Pair.of(p.getPetId(), p.getServiceId()), Collectors.toList()));
        var timeZoneName = timeZoneNameFuture.join();

        return petDetails.stream()
                .collect(Collectors.groupingBy(
                        petDetail -> petDetail.getPetId().longValue(),
                        Collectors.mapping(
                                petDetail -> {
                                    var service = idToService.getOrDefault(
                                            petDetail.getServiceId().longValue(),
                                            CustomizedServiceView.getDefaultInstance());
                                    var tax =
                                            idToTax.getOrDefault(service.getTaxId(), TaxRuleModel.getDefaultInstance());
                                    var splitLodgings = petDetailIdToSplitLodgings.getOrDefault(
                                            petDetail.getId().longValue(), List.of());
                                    var pricingRuleLogs = petServiceToPricingRuleLogs.getOrDefault(
                                            Pair.of(
                                                    petDetail.getPetId().longValue(),
                                                    petDetail.getServiceId().longValue()),
                                            List.of());
                                    var staff =
                                            staffMap.get(petDetail.getStaffId().longValue());
                                    return convertPetDetailToServiceItem(
                                            petDetail,
                                            service,
                                            staff,
                                            tax,
                                            idToLodging,
                                            idToLodgingType,
                                            splitLodgings,
                                            timeZoneName,
                                            pricingRuleLogs);
                                },
                                Collectors.toList())));
    }

    public static ServiceItem convertPetDetailToServiceItem(
            MoeGroomingPetDetail detail,
            CustomizedServiceView service,
            @Nullable StaffModel staff,
            TaxRuleModel tax,
            Map<Long, LodgingUnitModel> idToLodging,
            Map<Long, LodgingTypeModel> idToLodgingType,
            List<BoardingSplitLodging> splitLodgings,
            String timeZoneName,
            List<PricingRuleRecordApplyLog> pricingRuleApplyLogs) {
        return ServiceItem.newBuilder()
                .setServiceId(detail.getServiceId())
                .setName(service.getName())
                .setStaffId(detail.getStaffId())
                .setDescription(service.getDescription())
                .setServiceItemType(ServiceItemType.forNumber(detail.getServiceItemType()))
                .setServiceType(ServiceType.forNumber(detail.getServiceType()))
                .setDetailId(detail.getId())
                .setQuantity(detail.getQuantity())
                .setUnitPrice(MoneyUtils.toGoogleMoney(detail.getServicePrice()))
                .setTotalPrice(MoneyUtils.toGoogleMoney(detail.getTotalPrice()))
                .addAllLineItems(LineItemUtils.buildLineItems(
                        detail, staff, idToLodging, idToLodgingType, splitLodgings, timeZoneName, pricingRuleApplyLogs))
                .setTax(tax)
                .setExternalId(PET_DETAIL_EXTERNAL_ID_PREFIX + detail.getId())
                .setPriceUnit(ServicePriceUnit.forNumber(detail.getPriceUnit()))
                .setAssociatedServiceId(detail.getAssociatedServiceId())
                .build();
    }
}
