package com.moego.svc.appointment.service.params;

import com.moego.idl.models.appointment.v1.LodgingAssignInfo;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessPetSizeModel;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import java.util.List;
import java.util.Map;
import lombok.Builder;
import lombok.Getter;

@Builder
@Getter
public class BatchCheckInAutoAssignParam {
    List<Long> petIds;

    ServiceBriefView serviceBrief;

    String date;

    List<LodgingTypeModel> lodgingTypeList;

    List<LodgingUnitModel> lodgingUnitList;

    List<BusinessPetSizeModel> petSizeList;

    Map<Long, BusinessCustomerPetInfoModel> petMap;

    List<LodgingAssignInfo> assignInfoList;
}
