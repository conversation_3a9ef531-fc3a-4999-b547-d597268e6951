package com.moego.svc.appointment.service.remote;

import com.moego.idl.service.organization.v1.CompanyServiceGrpc.CompanyServiceBlockingStub;
import com.moego.idl.service.organization.v1.GetCompanyPreferenceSettingRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/9/9
 */
@Service
@RequiredArgsConstructor
public class CompanyRemoteService {

    private final CompanyServiceBlockingStub companyService;

    public String getTimezoneName(Long companyId) {
        return companyService
                .getCompanyPreferenceSetting(GetCompanyPreferenceSettingRequest.newBuilder()
                        .setCompanyId(companyId)
                        .build())
                .getPreferenceSetting()
                .getTimeZone()
                .getName();
    }
}
