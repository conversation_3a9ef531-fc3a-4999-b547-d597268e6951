package com.moego.svc.appointment.service;

import static com.moego.lib.common.exception.ExceptionUtil.bizException;

import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.idl.models.appointment.v1.LodgingAssignAppointmentInfo;
import com.moego.idl.models.appointment.v1.LodgingAssignInfo;
import com.moego.idl.models.appointment.v1.LodgingAssignPetDetailInfo;
import com.moego.idl.models.appointment.v1.LodgingAssignPetEvaluationInfo;
import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.service.offering.v1.GetLodgingTypeListRequest;
import com.moego.idl.service.offering.v1.GetLodgingUnitListRequest;
import com.moego.idl.service.offering.v1.LodgingTypeServiceGrpc;
import com.moego.idl.service.offering.v1.LodgingUnitServiceGrpc;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.util.Tx;
import com.moego.svc.appointment.converter.BoardingSplitLodgingConverter;
import com.moego.svc.appointment.converter.LodgingConverter;
import com.moego.svc.appointment.domain.BoardingSplitLodging;
import com.moego.svc.appointment.domain.EvaluationServiceDetail;
import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import com.moego.svc.appointment.service.params.BatchCheckInAutoAssignParam;
import com.moego.svc.appointment.service.remote.OrganizationRemoteService;
import com.moego.svc.appointment.utils.LodgingUtils;
import com.moego.svc.appointment.utils.PetDetailUtil;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class LodgingService {
    private final AppointmentServiceProxy appointmentService;
    private final PetDetailServiceProxy petDetailService;
    private final EvaluationServiceDetailService evaluationServiceDetailService;
    private final OrganizationRemoteService organizationRemoteService;
    private final LodgingUnitServiceGrpc.LodgingUnitServiceBlockingStub lodgingUnitStub;
    private final LodgingTypeServiceGrpc.LodgingTypeServiceBlockingStub lodgingTypeStub;
    private final BoardingSplitLodgingService boardingSplitLodgingService;

    public MoeGroomingPetDetail updateSplitLodging(
            MoeGroomingAppointment beforeAppointment,
            List<BoardingSplitLodging> boardingSplitLodgings,
            MoeGroomingPetDetail beforePetDetail,
            MoeGroomingPetDetail updatedPetDetail) {

        if (CollectionUtils.isEmpty(boardingSplitLodgings)) {
            return updatedPetDetail;
        }

        var currentSplitLodgings = boardingSplitLodgings.stream()
                .filter(splitLodging -> Objects.equals(
                        splitLodging.getPetDetailId(), updatedPetDetail.getId().longValue()))
                .sorted(Comparator.comparing(BoardingSplitLodging::getStartDateTime))
                .toList();
        if (CollectionUtils.isEmpty(currentSplitLodgings)) {
            return updatedPetDetail;
        }

        var newLodgingId = updateSplitLodgingDetail(
                updatedPetDetail,
                beforePetDetail,
                currentSplitLodgings,
                beforeAppointment.getId().longValue());
        if (CommonUtil.isNormal(newLodgingId)) {
            updatedPetDetail.setLodgingId(newLodgingId);
        }

        return updatedPetDetail;
    }

    public List<MoeGroomingPetDetail> updateSplitLodging(
            MoeGroomingAppointment beforeAppointment,
            List<MoeGroomingPetDetail> beforePetDetails,
            List<MoeGroomingPetDetail> updatedPetDetails) {

        var boardingSplitLodgings = boardingSplitLodgingService.getBoardingSplitLodgings(
                List.of(beforeAppointment.getId().longValue()));

        if (CollectionUtils.isEmpty(boardingSplitLodgings)) {
            return updatedPetDetails;
        }

        List<MoeGroomingPetDetail> newUpdatedPetDetails = new ArrayList<>();
        for (var updatedPetDetail : updatedPetDetails) {
            var currentSplitLodgings = boardingSplitLodgings.stream()
                    .filter(splitLodging -> Objects.equals(
                            splitLodging.getPetDetailId(),
                            updatedPetDetail.getId().longValue()))
                    .sorted(Comparator.comparing(BoardingSplitLodging::getStartDateTime))
                    .toList();
            if (CollectionUtils.isEmpty(currentSplitLodgings)) {
                newUpdatedPetDetails.add(updatedPetDetail);
                continue;
            }
            var currentBeforePetDetail = beforePetDetails.stream()
                    .filter(beforePetDetail -> Objects.equals(
                            beforePetDetail.getId().longValue(),
                            updatedPetDetail.getId().longValue()))
                    .findFirst();
            if (currentBeforePetDetail.isEmpty()) {
                newUpdatedPetDetails.add(updatedPetDetail);
                continue;
            }

            var newLodgingId = updateSplitLodgingDetail(
                    updatedPetDetail,
                    currentBeforePetDetail.get(),
                    currentSplitLodgings,
                    beforeAppointment.getId().longValue());
            if (CommonUtil.isNormal(newLodgingId)) {
                updatedPetDetail.setLodgingId(newLodgingId);
            }
            newUpdatedPetDetails.add(updatedPetDetail);
        }

        return newUpdatedPetDetails;
    }

    private Long updateSplitLodgingDetail(
            final MoeGroomingPetDetail updatedPetDetail,
            final MoeGroomingPetDetail currentBeforePetDetail,
            final List<BoardingSplitLodging> currentSplitLodgings,
            long appointmentId) {
        var oldStartDateTime = BoardingSplitLodgingConverter.INSTANCE.toLocalDateTime(
                currentBeforePetDetail.getStartDate(),
                currentBeforePetDetail.getStartTime().intValue());
        var oldEndDateTime = BoardingSplitLodgingConverter.INSTANCE.toLocalDateTime(
                currentBeforePetDetail.getEndDate(),
                currentBeforePetDetail.getEndTime().intValue());
        var newStartDateTime = BoardingSplitLodgingConverter.INSTANCE.toLocalDateTime(
                updatedPetDetail.getStartDate(), updatedPetDetail.getStartTime().intValue());
        var newEndDateTime = BoardingSplitLodgingConverter.INSTANCE.toLocalDateTime(
                updatedPetDetail.getEndDate(), updatedPetDetail.getEndTime().intValue());

        Long newLodgingId = null;

        // Creating a new list to avoid modifying the original list
        List<BoardingSplitLodging> adjustedLodgings = new ArrayList<>(currentSplitLodgings);

        if (newStartDateTime.isAfter(oldEndDateTime) || newEndDateTime.isBefore(oldStartDateTime)) {
            newLodgingId = currentSplitLodgings.stream()
                    .map(BoardingSplitLodging::getLodgingId)
                    .filter(CommonUtil::isNormal)
                    .findFirst()
                    .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR));
            adjustedLodgings = new ArrayList<>();
        }

        // Case 1: End date moved earlier - remove lodgings after new end date
        if (newEndDateTime.isBefore(oldEndDateTime)) {
            adjustedLodgings.removeIf(lodging -> lodging.getStartDateTime().isAfter(newEndDateTime)
                    || lodging.getStartDateTime().isEqual(newEndDateTime));

            // Adjust the last remaining lodging's end date if needed
            if (!adjustedLodgings.isEmpty()) {
                BoardingSplitLodging lastLodging = adjustedLodgings.get(adjustedLodgings.size() - 1);
                if (lastLodging.getEndDateTime().isAfter(newEndDateTime)) {
                    lastLodging.setEndDateTime(newEndDateTime);
                }
            }
        }

        // Case 2: Start date moved later - remove lodgings before new start date
        if (newStartDateTime.isAfter(oldStartDateTime)) {
            adjustedLodgings.removeIf(lodging -> lodging.getEndDateTime().isBefore(newStartDateTime)
                    || lodging.getEndDateTime().isEqual(newStartDateTime));

            // Adjust the first remaining lodging's start date if needed
            if (!adjustedLodgings.isEmpty()) {
                BoardingSplitLodging firstLodging = adjustedLodgings.get(0);
                if (firstLodging.getStartDateTime().isBefore(newStartDateTime)) {
                    firstLodging.setStartDateTime(newStartDateTime);
                }
            }
        }

        // Case 3: End date moved later - extend the last lodging's end date
        if (newEndDateTime.isAfter(oldEndDateTime) && !adjustedLodgings.isEmpty()) {
            BoardingSplitLodging lastLodging = adjustedLodgings.get(adjustedLodgings.size() - 1);
            lastLodging.setEndDateTime(newEndDateTime);
        }

        // Case 4: Start date moved earlier - adjust the first lodging's start date
        if (newStartDateTime.isBefore(oldStartDateTime) && !adjustedLodgings.isEmpty()) {
            BoardingSplitLodging firstLodging = adjustedLodgings.get(0);
            firstLodging.setStartDateTime(newStartDateTime);
        }

        if (adjustedLodgings.size() == 1) {
            LocalDateTime start = adjustedLodgings.stream()
                    .map(BoardingSplitLodging::getStartDateTime)
                    .min(Comparator.naturalOrder())
                    .orElse(null);
            LocalDateTime end = adjustedLodgings.stream()
                    .map(BoardingSplitLodging::getEndDateTime)
                    .max(Comparator.naturalOrder())
                    .orElse(null);
            if (Objects.equals(start, newStartDateTime) && Objects.equals(end, newEndDateTime)) {
                newLodgingId = adjustedLodgings.get(0).getLodgingId();
                adjustedLodgings = List.of();
            }
        }

        final List<BoardingSplitLodging> finalAdjustedLodgings = adjustedLodgings;
        Tx.doInTransaction(() -> {
            boardingSplitLodgingService.deleteByIds(
                    appointmentId,
                    currentSplitLodgings.stream()
                            .map(BoardingSplitLodging::getId)
                            .toList());
            boardingSplitLodgingService.saveBoardingSplitLodging(finalAdjustedLodgings);
        });

        return newLodgingId;
    }

    public List<LodgingAssignInfo> getAssignInfo(
            Long companyId, Long businessId, String startDate, String endDate, List<Long> lodgingIds) {
        if (companyId == null) {
            companyId = organizationRemoteService.getCompanyIdByBusinessId(businessId.intValue());
        }
        // 拉取时间段内 appointment 信息
        List<MoeGroomingAppointment> appointments = appointmentService.getAppointmentsByDateRange(
                companyId,
                businessId.intValue(),
                LocalDate.parse(startDate),
                LocalDate.parse(endDate),
                List.of(ServiceItemType.BOARDING, ServiceItemType.DAYCARE, ServiceItemType.EVALUATION));
        List<Long> appointmentIdList =
                appointments.stream().map(k -> k.getId().longValue()).toList();
        if (CollectionUtils.isEmpty(appointmentIdList)) {
            return List.of();
        }
        Map<Long, MoeGroomingAppointment> appointmentMap =
                appointments.stream().collect(Collectors.toMap(k -> k.getId().longValue(), Function.identity()));

        // 拉取 appointment 对应的 pet service detail 信息
        List<MoeGroomingPetDetail> petDetails = petDetailService.getPetDetailList(appointmentIdList);
        petDetailService.fixServiceType(companyId, petDetails);
        Map<Integer, PetDetailDateType> dateTypeMap = PetDetailUtil.getDateTypeMap(petDetails);
        List<EvaluationServiceDetail> petEvaluations =
                evaluationServiceDetailService.getPetEvaluationList(appointmentIdList);

        // boarding split lodgings
        var boardingSplitLodgings = boardingSplitLodgingService.getBoardingSplitLodgings(appointmentMap.keySet());

        petDetails = petDetails.stream()
                .filter(k -> ServiceType.SERVICE_VALUE == k.getServiceType())
                .filter(k -> CollectionUtils.isEmpty(lodgingIds)
                        || lodgingIds.contains(k.getLodgingId())
                        || boardingSplitLodgings.stream()
                                .anyMatch(boardingSplitLodging -> Objects.equals(
                                                boardingSplitLodging.getPetDetailId(),
                                                k.getId().longValue())
                                        && lodgingIds.contains(boardingSplitLodging.getLodgingId())))
                .collect(Collectors.toList());
        petEvaluations = petEvaluations.stream()
                .filter(k -> CollectionUtils.isEmpty(lodgingIds) || lodgingIds.contains(k.getLodgingId()))
                .toList();

        return buildAssignInfo(
                appointmentMap,
                dateTypeMap,
                petDetailService.getWithActualDatesInfo(petDetails),
                petEvaluations,
                boardingSplitLodgings);
    }

    List<LodgingAssignInfo> buildAssignInfo(
            Map<Long, MoeGroomingAppointment> appointmentMap,
            Map<Integer, PetDetailDateType> dateTypeMap,
            List<MoeGroomingPetDetail> petDetails,
            List<EvaluationServiceDetail> petEvaluations,
            final List<BoardingSplitLodging> boardingSplitLodgings) {

        var hasSplitLodgingPetDetailIds = boardingSplitLodgings.stream()
                .map(BoardingSplitLodging::getPetDetailId)
                .collect(Collectors.toUnmodifiableSet());

        Map<Long, Set<Long>> lodgingAppointmentIds = new HashMap<>();
        // 按 lodging、appointment 分组
        Map<Long, Map<Long, List<MoeGroomingPetDetail>>> petDetailsMap = new HashMap<>();
        Map<Long, Map<Long, List<EvaluationServiceDetail>>> petEvaluationsMap = new HashMap<>();
        petDetails.forEach(petDetail -> {
            var noSplitLodging =
                    !hasSplitLodgingPetDetailIds.contains(petDetail.getId().longValue());
            if (noSplitLodging) {
                lodgingAppointmentIds
                        .computeIfAbsent(petDetail.getLodgingId(), k -> new HashSet<>())
                        .add(petDetail.getGroomingId().longValue());
                petDetailsMap
                        .computeIfAbsent(petDetail.getLodgingId(), k -> new HashMap<>())
                        .computeIfAbsent(petDetail.getGroomingId().longValue(), k -> new ArrayList<>())
                        .add(petDetail);
            }
        });

        boardingSplitLodgings.forEach(boardingSplitLodging -> {
            lodgingAppointmentIds
                    .computeIfAbsent(boardingSplitLodging.getLodgingId(), k -> new HashSet<>())
                    .add(boardingSplitLodging.getAppointmentId());
            var groomingPetDetail = petDetails.stream()
                    .filter(petDetail -> Objects.equals(
                                    petDetail.getId().longValue(), boardingSplitLodging.getPetDetailId())
                            && Objects.equals(
                                    petDetail.getGroomingId().longValue(), boardingSplitLodging.getAppointmentId())
                            && Objects.equals(petDetail.getPetId().longValue(), boardingSplitLodging.getPetId()))
                    .findFirst()
                    .orElse(null);
            if (Objects.isNull(groomingPetDetail)) {
                return;
            }

            var moeGroomingPetDetails = petDetailsMap
                    .computeIfAbsent(boardingSplitLodging.getLodgingId(), k -> new HashMap<>())
                    .computeIfAbsent(boardingSplitLodging.getAppointmentId(), k -> new ArrayList<>());
            if (moeGroomingPetDetails.stream()
                    .anyMatch(petDetail -> Objects.equals(petDetail.getId(), groomingPetDetail.getId()))) {
                return;
            }
            moeGroomingPetDetails.add(groomingPetDetail);
        });

        petEvaluations.forEach(petEvaluation -> {
            lodgingAppointmentIds
                    .computeIfAbsent(petEvaluation.getLodgingId(), k -> new HashSet<>())
                    .add(petEvaluation.getAppointmentId());
            petEvaluationsMap
                    .computeIfAbsent(petEvaluation.getLodgingId(), k -> new HashMap<>())
                    .computeIfAbsent(petEvaluation.getAppointmentId(), k -> new ArrayList<>())
                    .add(petEvaluation);
        });

        List<LodgingAssignInfo> assignInfoList = new ArrayList<>();
        lodgingAppointmentIds.forEach(
                (lodgingId, appointmentIds) -> {
                    List<LodgingAssignAppointmentInfo> appointmentInfoList = new ArrayList<>();
                    appointmentIds.forEach(appointmentId -> {
                        if (!appointmentMap.containsKey(appointmentId)) {
                            return;
                        }
                        MoeGroomingAppointment appointment = appointmentMap.get(appointmentId);
                        appointmentInfoList.add(LodgingAssignAppointmentInfo.newBuilder()
                                .setId(appointmentId)
                                .setCustomerId(appointment.getCustomerId().longValue())
                                .addAllPetDetails(petDetailsMap
                                        .getOrDefault(lodgingId, new HashMap<>())
                                        .getOrDefault(appointmentId, List.of())
                                        .stream()
                                        .map(petDetail -> LodgingConverter.INSTANCE.buildLodgingAssignPetDetailInfos(
                                                petDetail, dateTypeMap, boardingSplitLodgings, lodgingId))
                                        .flatMap(List::stream)
                                        .toList())
                                .addAllPetEvaluations(petEvaluationsMap
                                        .getOrDefault(lodgingId, new HashMap<>())
                                        .getOrDefault(appointmentId, List.of())
                                        .stream()
                                        .map(LodgingConverter.INSTANCE::buildLodgingAssignPetEvaluationInfo)
                                        .toList())
                                .build());
                    });
                    assignInfoList.add(LodgingAssignInfo.newBuilder()
                            .setLodgingId(lodgingId)
                            .addAllAppointments(appointmentInfoList)
                            .build());
                });
        return assignInfoList;
    }

    public void transfer(Long companyId, Long businessId, Long fromLodgingId, Long toLodgingId) {
        if (fromLodgingId <= 0 || toLodgingId <= 0) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "invalid lodging id");
        }
        List<Long> upcomingAppointmentIds = listUpcomingAppointment(companyId, businessId);
        List<Integer> petDetailIdsToUpdate = petDetailService.getPetDetailList(upcomingAppointmentIds).stream()
                .filter(k -> Objects.equals(k.getLodgingId(), fromLodgingId))
                .map(MoeGroomingPetDetail::getId)
                .toList();
        List<Long> petEvaluationIdsToUpdate =
                evaluationServiceDetailService.getPetEvaluationList(upcomingAppointmentIds).stream()
                        .filter(k -> Objects.equals(k.getLodgingId(), fromLodgingId))
                        .map(EvaluationServiceDetail::getId)
                        .toList();

        petDetailService.updateLodging(petDetailIdsToUpdate, toLodgingId);
        evaluationServiceDetailService.updateLodging(petEvaluationIdsToUpdate, toLodgingId);
    }

    public List<Long> listUpcomingAppointment(Long companyId, @Nullable Long businessId) {
        String curDate = organizationRemoteService.getCompanyCurrentDate(companyId);
        return appointmentService
                .listUpcomingAppointment(companyId, businessId == null ? null : businessId.intValue(), curDate, 0)
                .stream()
                .map(Integer::longValue)
                .toList();
    }

    /**
     * @return lodgignId -> upcoming appointmentIds
     */
    public Map<Long, Set<Long>> getUpcomingAppointment(
            Long companyId, @Nullable Long businessId, List<Long> lodgingIds) {
        Map<Long, Set<Long>> result = new HashMap<>();
        if (CollectionUtils.isEmpty(lodgingIds)) {
            return result;
        }
        List<Long> upcomingAppointmentIds = listUpcomingAppointment(companyId, businessId);
        Set<Long> targetLodgingIds = new HashSet<>(lodgingIds);
        petDetailService.getPetDetailList(upcomingAppointmentIds).forEach(k -> {
            if (targetLodgingIds.contains(k.getLodgingId())) {
                result.computeIfAbsent(k.getLodgingId(), v -> new HashSet<>())
                        .add(k.getGroomingId().longValue());
            }
        });

        evaluationServiceDetailService
                .getPetEvaluationList(upcomingAppointmentIds)
                .forEach(k -> {
                    if (targetLodgingIds.contains(k.getLodgingId())) {
                        result.computeIfAbsent(k.getLodgingId(), v -> new HashSet<>())
                                .add(k.getAppointmentId());
                    }
                });
        return result;
    }

    public List<LodgingUnitModel> listLodgings(long companyId) {
        return lodgingUnitStub
                .getLodgingUnitList(GetLodgingUnitListRequest.newBuilder()
                        .setCompanyId(companyId)
                        .build())
                .getLodgingUnitListList();
    }

    public List<LodgingTypeModel> listLodgingTypes(long companyId) {
        return lodgingTypeStub
                .getLodgingTypeList(GetLodgingTypeListRequest.newBuilder()
                        .setCompanyId(companyId)
                        .build())
                .getLodgingTypeListList();
    }

    /**
     * This method is used to automatically assign pets to lodgings.
     *
     * @param param The parameters for the batch check-in auto assign.
     * @return A map of pet ID to lodging unit ID.
     */
    public static Map<Long, Long> batchCheckInAutoAssignLodging(BatchCheckInAutoAssignParam param) {

        var lodgingTypeList = param.getLodgingTypeList();
        var lodgingUnitList = param.getLodgingUnitList();
        var petCntPerLodging = calPetCntPerLodging(param.getDate(), param.getAssignInfoList());

        var availableLodgingType = LodgingService.filterLodgingTypeByService(lodgingTypeList, param.getServiceBrief());

        var result = new HashMap<Long, Long>();
        for (Long petId : param.getPetIds()) {
            if (!param.getPetMap().containsKey(petId)) {
                continue;
            }

            var pet = param.getPetMap().get(petId);

            // 每次分配前重新计算lodging状态，包含之前已分配的pet
            var lodgingStatusMap = LodgingUtils.calLodgingStatus(lodgingTypeList, lodgingUnitList, petCntPerLodging);

            Long petSizeId = PetDetailUtil.getPetSizeId(pet.getWeight(), param.getPetSizeList());
            var availableLodgingTypeMap =
                    LodgingService.filterLodgingTypeByPetSize(availableLodgingType, petSizeId).stream()
                            .collect(Collectors.toMap(LodgingTypeModel::getId, Function.identity()));
            var availableLodgingUnitMap = param.getLodgingUnitList().stream()
                    .filter(lodgingUnit -> availableLodgingTypeMap.containsKey(lodgingUnit.getLodgingTypeId()))
                    .sorted(Comparator.comparing(
                                    (LodgingUnitModel lodgingUnit) -> LodgingUtils.LODGING_STATUS_ORDER_MAP.get(
                                            lodgingStatusMap.get(lodgingUnit.getId())))
                            .thenComparing(LodgingUnitModel::getId))
                    .toList();

            var petCntNeedPerDay = Map.of(param.getDate(), 1);

            long lodgingUnitId = 0;
            for (LodgingUnitModel lodgingUnit : availableLodgingUnitMap) {
                var lodgingType = availableLodgingTypeMap.get(lodgingUnit.getLodgingTypeId());
                if (lodgingType == null) {
                    continue;
                }

                Map<String, Integer> petAllocatedPerDay =
                        Map.of(param.getDate(), petCntPerLodging.getOrDefault(lodgingUnit.getId(), 0));
                if (LodgingUtils.isLodgingAvailable(lodgingType, petCntNeedPerDay, petAllocatedPerDay)) {
                    lodgingUnitId = lodgingUnit.getId();
                    break;
                }
            }

            if (lodgingUnitId != 0) {
                petCntPerLodging.put(lodgingUnitId, petCntPerLodging.getOrDefault(lodgingUnitId, 0) + 1);
                result.put(petId, lodgingUnitId);
            }
        }
        return result;
    }

    // return <key: lodgingUnitId, value: petCnt>
    private static Map<Long, Integer> calPetCntPerLodging(String date, List<LodgingAssignInfo> assignInfoList) {
        if (CollectionUtils.isEmpty(assignInfoList)) {
            return new HashMap<>();
        }

        // <key: lodgingId, value: Set<petId>>
        Map<Long, Set<Integer>> petsPerLodging = new HashMap<>();

        // 收集 pet 寄养信息
        for (LodgingAssignInfo assignInfo : assignInfoList) {
            for (LodgingAssignAppointmentInfo appointmentInfo : assignInfo.getAppointmentsList()) {
                appointmentInfo
                        .getPetDetailsList()
                        .forEach(k -> collectPetDetailLodgingCnt(assignInfo.getLodgingId(), k, date, petsPerLodging));

                appointmentInfo
                        .getPetEvaluationsList()
                        .forEach(k ->
                                collectPetEvaluationLodgingCnt(assignInfo.getLodgingId(), k, date, petsPerLodging));
            }
        }

        return petsPerLodging.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, k -> k.getValue().size()));
    }

    // 收集 pet service detail 占用的 lodging 排期信息
    private static void collectPetDetailLodgingCnt(
            Long lodgingUnitId,
            LodgingAssignPetDetailInfo petDetail,
            String collectDate,
            Map<Long, Set<Integer>> petsPerLodging) {
        List<String> dates = petDetail.getSpecificDatesList();
        if (CollectionUtils.isEmpty(dates)) {
            dates = DateUtil.generateAllDatesBetween(petDetail.getStartDate(), petDetail.getEndDate());
        }
        if (dates.contains(collectDate)) {
            petsPerLodging.computeIfAbsent(lodgingUnitId, k -> new HashSet<>()).add(petDetail.getPetId());
        }
    }

    // 收集 pet service detail 占用的 lodging 排期信息
    private static void collectPetEvaluationLodgingCnt(
            Long lodgingUnitId,
            LodgingAssignPetEvaluationInfo petEvaluation,
            String collectDate,
            Map<Long, Set<Integer>> petsPerLodging) {
        List<String> dates = DateUtil.generateAllDatesBetween(petEvaluation.getStartDate(), petEvaluation.getEndDate());
        if (dates.contains(collectDate)) {
            petsPerLodging.computeIfAbsent(lodgingUnitId, k -> new HashSet<>()).add(petEvaluation.getPetId());
        }
    }

    public static List<LodgingTypeModel> filterLodgingTypeByPetSize(
            List<LodgingTypeModel> lodgingTypeList, Long petSizeId) {
        if (CollectionUtils.isEmpty(lodgingTypeList)) {
            return new ArrayList<>();
        }
        return lodgingTypeList.stream()
                .filter(k -> {
                    if (!k.getPetSizeFilter()) {
                        return true;
                    }
                    if (petSizeId == null) {
                        return false;
                    }
                    return k.getPetSizeIdsList().contains(petSizeId);
                })
                .toList();
    }

    public static List<LodgingTypeModel> filterLodgingTypeByService(
            List<LodgingTypeModel> lodgingTypeList, ServiceBriefView service) {
        if (CollectionUtils.isEmpty(lodgingTypeList)) {
            return new ArrayList<>();
        }
        // daycare service 选中 all lodging types 时，不 auto assign
        if (!service.getLodgingFilter() && service.getServiceItemType() == ServiceItemType.DAYCARE) {
            return List.of();
        } else if (!service.getLodgingFilter()) {
            return lodgingTypeList;
        }
        return lodgingTypeList.stream()
                .filter(k -> service.getCustomizedLodgingsList().contains(k.getId()))
                .toList();
    }
}
