package com.moego.svc.appointment.consumer;

import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.enums.InvoiceStatusEnum;
import com.moego.common.enums.order.OrderItemType;
import com.moego.idl.models.appointment.v1.AppointmentPaymentStatus;
import com.moego.idl.models.appointment.v1.AppointmentSource;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.event_bus.v1.EventData;
import com.moego.idl.models.event_bus.v1.OrderEvent;
import com.moego.idl.models.order.v1.OrderItemModel;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.models.order.v1.OrderModelV1;
import com.moego.idl.models.order.v1.OrderSourceType;
import com.moego.idl.models.order.v1.OrderStatus;
import com.moego.idl.service.order.v1.GetOrderDetailRequest;
import com.moego.idl.service.order.v1.ListOrdersV1Request;
import com.moego.idl.service.order.v1.OrderServiceGrpc;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.proto.MoneyUtils;
import com.moego.lib.common.util.Tx;
import com.moego.lib.event_bus.consumer.AbstractConsumer;
import com.moego.lib.event_bus.event.EventRecord;
import com.moego.server.grooming.dto.appointment.history.UpdateStatusLogDTO;
import com.moego.server.grooming.enums.AppointmentAction;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLogRecorder;
import com.moego.svc.appointment.domain.EvaluationServiceDetail;
import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import com.moego.svc.appointment.domain.ServiceChargeDetail;
import com.moego.svc.appointment.service.AppointmentExtraInfoService;
import com.moego.svc.appointment.service.AppointmentServiceProxy;
import com.moego.svc.appointment.service.EvaluationServiceDetailService;
import com.moego.svc.appointment.service.PetDetailServiceProxy;
import com.moego.svc.appointment.service.ServiceChargeDetailService;
import com.moego.svc.appointment.utils.LineItemUtils;
import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2025/5/25
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderConsumer extends AbstractConsumer<EventData> {

    private final OrderServiceGrpc.OrderServiceBlockingStub orderStub;
    private final AppointmentServiceProxy appointmentService;
    private final AppointmentExtraInfoService extraInfoService;
    private final PetDetailServiceProxy petDetailService;
    private final EvaluationServiceDetailService evaluationServiceDetailService;
    private final ServiceChargeDetailService serviceChargeDetailService;

    private static final Set<String> SHOULD_UPDATE_LINE_ITEM_TYPES = Set.of(
            OrderItemType.ITEM_TYPE_SERVICE.getType(),
            OrderItemType.ITEM_TYPE_EVALUATION_SERVICE.getType(),
            OrderItemType.ITEM_TYPE_SERVICE_CHARGE.getType());

    @Override
    protected String topicName() {
        return "moego.order";
    }

    @Override
    protected void consume(EventRecord<EventData> event) {
        switch (event.type()) {
            case ORDER_CREATED -> processOrderCreated(event.detail().getOrderEvent());
            case ORDER_COMPLETED -> processOrderCompleted(event.detail().getOrderEvent());
            default -> {} // No-op
        }
    }

    private void processOrderCreated(OrderEvent orderEvent) {
        if (!Objects.equals(orderEvent.getSourceType(), OrderSourceType.APPOINTMENT)
                || !Objects.equals(orderEvent.getOrderType(), OrderModel.OrderType.DEPOSIT)) {
            return;
        }
        var isNewOrder = extraInfoService.isNewOrder(orderEvent.getSourceId());
        if (!isNewOrder) {
            return;
        }
        log.info("consume order_created, event: {}", orderEvent);

        processOrderLineItemId(orderEvent.getBusinessId(), orderEvent.getId(), orderEvent.getSourceId());
    }

    private void processOrderLineItemId(long businessId, long orderId, long appointmentId) {
        var orderDetail = orderStub
                .getOrderDetailV1(GetOrderDetailRequest.newBuilder()
                        .setBusinessId(businessId)
                        .setOrderId(orderId)
                        .build())
                .getOrder();
        var lineItems = orderDetail.getOrderItemsList().stream()
                .filter(i -> SHOULD_UPDATE_LINE_ITEM_TYPES.contains(i.getType()))
                .toList();
        if (CollectionUtils.isEmpty(lineItems)) {
            return;
        }
        var externalUuidToLineItem = lineItems.stream()
                .collect(Collectors.toMap(OrderItemModel::getExternalUuid, Function.identity(), (a, b) -> a));

        Tx.doInTransaction(() -> {
            processPetDetails(appointmentId, externalUuidToLineItem);

            processPetEvaluations(appointmentId, externalUuidToLineItem);

            processServiceCharges(appointmentId, externalUuidToLineItem);
        });
    }

    private void processPetDetails(long appointmentId, Map<String, OrderItemModel> externalUuidToLineItem) {
        var petDetails = petDetailService.getPetDetailList(appointmentId);

        var updatedPetDetails = petDetails.stream()
                .map(petDetail -> {
                    var externalUuid = LineItemUtils.PET_DETAIL_EXTERNAL_ID_PREFIX + petDetail.getId();
                    var lineItem = externalUuidToLineItem.get(externalUuid);
                    if (lineItem == null) {
                        return null;
                    }
                    var update = new MoeGroomingPetDetail();
                    update.setId(petDetail.getId());
                    update.setOrderLineItemId(lineItem.getId());
                    return update;
                })
                .filter(Objects::nonNull)
                .toList();

        petDetailService.updatePetDetailById(updatedPetDetails);
    }

    private void processPetEvaluations(long appointmentId, Map<String, OrderItemModel> externalUuidToLineItem) {
        var petEvaluations = evaluationServiceDetailService.getPetEvaluationList(appointmentId);

        var updatedPetEvaluations = petEvaluations.stream()
                .map(evaluationServiceDetail -> {
                    var externalUuid = LineItemUtils.EVALUATION_EXTERNAL_ID_PREFIX + evaluationServiceDetail.getId();
                    var lineItem = externalUuidToLineItem.get(externalUuid);
                    if (lineItem == null) {
                        return null;
                    }
                    var update = new EvaluationServiceDetail();
                    update.setId(evaluationServiceDetail.getId());
                    update.setOrderLineItemId(lineItem.getId());
                    return update;
                })
                .filter(Objects::nonNull)
                .toList();

        evaluationServiceDetailService.updatePetEvaluationById(updatedPetEvaluations);
    }

    private void processServiceCharges(long appointmentId, Map<String, OrderItemModel> externalUuidToLineItem) {
        var serviceChargeDetails = serviceChargeDetailService.listByAppointmentId(appointmentId);

        var updatedServiceChargeDetails = serviceChargeDetails.stream()
                .map(serviceChargeDetail -> {
                    var externalUuid = LineItemUtils.SERVICE_CHARGE_EXTERNAL_ID_PREFIX + serviceChargeDetail.getId();
                    var lineItem = externalUuidToLineItem.get(externalUuid);
                    if (lineItem == null) {
                        return null;
                    }
                    var update = new ServiceChargeDetail();
                    update.setId(serviceChargeDetail.getId());
                    update.setOrderLineItemId(lineItem.getId());
                    return update;
                })
                .filter(Objects::nonNull)
                .toList();

        serviceChargeDetailService.updateById(updatedServiceChargeDetails);
    }

    private void processOrderCompleted(OrderEvent orderEvent) {
        if (!Objects.equals(OrderStatus.COMPLETED, orderEvent.getOrderStatus())
                || !Objects.equals(OrderSourceType.APPOINTMENT, orderEvent.getSourceType())) {
            return;
        }
        log.info("consume order_completed, event: {}", orderEvent);

        var orderId = orderEvent.getId();
        var businessId = orderEvent.getBusinessId();

        var request = ListOrdersV1Request.newBuilder()
                .setBusinessId(businessId)
                .setOriginOrderId(orderId)
                .build();
        var orders = orderStub.listOrdersV1(request).getOrdersList();

        // only all orders with a remain amount of zero can be considered fully paid
        var hasFullyPaid = orders.stream()
                .map(OrderModelV1::getRemainAmount)
                .allMatch(amount -> MoneyUtils.fromGoogleMoney(amount).compareTo(BigDecimal.ZERO) == 0);

        if (extraInfoService.isNewOrder(orderEvent.getSourceId())) {
            orders.stream()
                    .filter(o -> Objects.equals(o.getId(), orderEvent.getId()))
                    .findAny()
                    .ifPresent(order -> Tx.doInTransaction(() -> {
                        // 幂等处理 order_line_item_id 关联关系，避免 completed 乱序先于 created event
                        processOrderLineItemId(
                                orderEvent.getBusinessId(), orderEvent.getId(), orderEvent.getSourceId());

                        updateIsPaid(order, hasFullyPaid);

                        updateStatus(order);

                        updateNoShowFee(order);
                    }));
        } else {
            // 非 new order 且订单全部支付完成，更新 appointment 状态为 PAID
            if (hasFullyPaid) {
                var update = new MoeGroomingAppointment();
                update.setId((int) orderEvent.getSourceId());
                update.setIsPaid(GroomingAppointmentEnum.PAID);
                appointmentService.update(update);
            }
        }
    }

    private void updateIsPaid(OrderModelV1 order, boolean hasFullyPaid) {
        var update = new MoeGroomingAppointment();
        update.setId((int) order.getSourceId());
        update.setIsPaid(getIsPaid(order.getSourceId(), hasFullyPaid));
        appointmentService.update(update);
    }

    private byte getIsPaid(long appointmentId, boolean hasFullyPaid) {
        var allHasLineItem = allPetAndEvaluationHaveOrderLineItem(appointmentId);

        var idPaid = allHasLineItem && hasFullyPaid
                ? AppointmentPaymentStatus.FULLY_PAID
                : AppointmentPaymentStatus.PARTIAL_PAID;

        return (byte) idPaid.getNumber();
    }

    private void updateStatus(OrderModelV1 order) {
        var appointment = appointmentService.mustGet(order.getSourceId());
        // 定金支付成功后自动confirm，OB定金除外
        var isDeposit = Objects.equals(order.getOrderType(), OrderModel.OrderType.DEPOSIT);
        var isFromOB = Objects.equals(appointment.getSource(), AppointmentSource.ONLINE_BOOKING_VALUE);
        if (!isDeposit || isFromOB) {
            return;
        }
        var update = new MoeGroomingAppointment();
        update.setId(appointment.getId());
        switch (AppointmentStatusEnum.values()[appointment.getStatus()]) {
            case UNCONFIRMED -> {
                update.setStatus(AppointmentStatusEnum.CONFIRMED.getValue());
                update.setConfirmedTime(update.getUpdateTime());
                update.setConfirmByType(GroomingAppointmentEnum.CONFIRM_TYPE_BY_DEPOSIT);
            }
            case CHECK_IN -> update.setStatusBeforeCheckin((byte) AppointmentStatus.CONFIRMED_VALUE);
            case READY -> {
                update.setStatusBeforeCheckin((byte) AppointmentStatus.CONFIRMED_VALUE);
                if (Objects.equals(
                                (byte) AppointmentStatus.APPOINTMENT_STATUS_UNSPECIFIED_VALUE,
                                appointment.getStatusBeforeReady())
                        || Objects.equals(
                                (byte) AppointmentStatus.UNCONFIRMED_VALUE, appointment.getStatusBeforeReady())) {
                    update.setStatusBeforeReady((byte) AppointmentStatus.CONFIRMED_VALUE);
                }
            }
            case FINISHED -> {
                update.setStatusBeforeCheckin((byte) AppointmentStatus.CONFIRMED_VALUE);
                if (Objects.equals(
                                (byte) AppointmentStatus.APPOINTMENT_STATUS_UNSPECIFIED_VALUE,
                                appointment.getStatusBeforeFinish())
                        || Objects.equals(
                                (byte) AppointmentStatus.UNCONFIRMED_VALUE, appointment.getStatusBeforeFinish())) {
                    update.setStatusBeforeFinish((byte) AppointmentStatus.CONFIRMED_VALUE);
                }
                if (Objects.equals(
                                (byte) AppointmentStatus.APPOINTMENT_STATUS_UNSPECIFIED_VALUE,
                                appointment.getStatusBeforeReady())
                        || Objects.equals(
                                (byte) AppointmentStatus.UNCONFIRMED_VALUE, appointment.getStatusBeforeReady())) {
                    update.setStatusBeforeReady((byte) AppointmentStatus.CONFIRMED_VALUE);
                }
            }
            default -> {}
        }

        var affectedRows = appointmentService.update(update);
        if (affectedRows == 0) {
            return;
        }

        ActivityLogRecorder.record(
                order.getBusinessId(),
                AppointmentAction.UPDATE_PAYMENT_STATUS,
                ResourceType.APPOINTMENT,
                update.getId(),
                update);

        var updateStatus = Objects.equals((byte) AppointmentStatus.UNCONFIRMED_VALUE, appointment.getStatus())
                && Objects.equals((byte) AppointmentStatus.CONFIRMED_VALUE, update.getStatus());
        if (updateStatus) {
            ActivityLogRecorder.record(
                    order.getBusinessId(),
                    Optional.ofNullable(AuthContext.get().staffId()).orElse(0L),
                    AppointmentAction.UPDATE_STATUS,
                    ResourceType.APPOINTMENT,
                    update.getId(),
                    new UpdateStatusLogDTO(AppointmentStatusEnum.UNCONFIRMED, AppointmentStatusEnum.CONFIRMED));
        }
    }

    private void updateNoShowFee(OrderModelV1 order) {
        if (!InvoiceStatusEnum.TYPE_NOSHOW.equals(order.getSourceType())) {
            return;
        }
        var appointment = appointmentService.mustGet(order.getSourceId());
        if (!Objects.equals(appointment.getStatus().intValue(), AppointmentStatus.CANCELED_VALUE)) {
            return;
        }
        // no-show order and appointment is canceled, update no-show fee
        var update = new MoeGroomingAppointment();
        update.setId((int) order.getSourceId());
        update.setNoShowFee(MoneyUtils.fromGoogleMoney(order.getPaidAmount()));
        appointmentService.update(update);

        ActivityLogRecorder.record(
                order.getBusinessId(),
                AppointmentAction.UPDATE_NO_SHOW,
                ResourceType.APPOINTMENT,
                update.getId(),
                update);
    }

    private boolean shouldMarkAsPaid(Long appointmentId) {
        if (!extraInfoService.isNewOrder(appointmentId)) {
            return true;
        }
        // DONE new order flow
        return allPetAndEvaluationHaveOrderLineItem(appointmentId);
    }

    private boolean allPetAndEvaluationHaveOrderLineItem(long appointmentId) {
        var petDetails = petDetailService.getPetDetailList(appointmentId);
        var evaluationDetails = evaluationServiceDetailService.getPetEvaluationList(appointmentId);
        var serviceChargeDetails = serviceChargeDetailService.listByAppointmentId(appointmentId);

        var petDetailHasOrderLineItem = petDetails.stream()
                .map(MoeGroomingPetDetail::getOrderLineItemId)
                .allMatch(OrderConsumer::hasOrderLineItem);

        var evaluationDetailHasOrderLineItem = evaluationDetails.stream()
                .map(EvaluationServiceDetail::getOrderLineItemId)
                .allMatch(OrderConsumer::hasOrderLineItem);

        var serviceChargeDetailHasOrderLineItem = serviceChargeDetails.stream()
                .map(ServiceChargeDetail::getOrderLineItemId)
                .allMatch(OrderConsumer::hasOrderLineItem);

        return petDetailHasOrderLineItem && evaluationDetailHasOrderLineItem && serviceChargeDetailHasOrderLineItem;
    }

    static boolean hasOrderLineItem(Long orderLineItemId) {
        return orderLineItemId != null && orderLineItemId > 0;
    }
}
