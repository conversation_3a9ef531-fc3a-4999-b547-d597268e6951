package com.moego.server.grooming.service.ob;

import static org.junit.jupiter.api.Assertions.*;

import com.moego.server.grooming.dto.BookOnlineDTO;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.params.BookOnlinePetParams;
import com.moego.server.grooming.params.BookOnlineSubmitParams;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * OBGroomingService 单元测试
 */
@ExtendWith(MockitoExtension.class)
public class OBGroomingServiceTest {

    @InjectMocks
    private OBGroomingService obGroomingService;

    // ========== canAutoAssign 方法的单元测试 ==========

    @Test
    public void testCanAutoAssign_AllConditionsMet_ReturnsTrue() {
        // 准备测试数据 - 所有条件都满足
        MoeBusinessBookOnline bookOnline = createBookOnline(BookOnlineDTO.UseVersion.OB_3_0);
        BookOnlineSubmitParams obParams = createSubmitParams(
                "2025-01-15", // 有日期
                null,         // 没有staffId
                true,         // noStartTime = true
                createPetDataWithoutStaff() // petData 中没有 staffId
        );

        // 执行测试
        boolean result = obGroomingService.canAutoAssign(bookOnline, obParams);

        // 验证结果
        assertTrue(result);
    }

    @Test
    public void testCanAutoAssign_NoAppointmentDate_ReturnsFalse() {
        // 准备测试数据 - 没有预约日期
        MoeBusinessBookOnline bookOnline = createBookOnline(BookOnlineDTO.UseVersion.OB_3_0);
        BookOnlineSubmitParams obParams = createSubmitParams(
                null,         // 没有日期
                null,         // 没有staffId
                true,         // noStartTime = true
                createPetDataWithoutStaff()
        );

        // 执行测试
        boolean result = obGroomingService.canAutoAssign(bookOnline, obParams);

        // 验证结果
        assertFalse(result);
    }

    @Test
    public void testCanAutoAssign_EmptyAppointmentDate_ReturnsFalse() {
        // 准备测试数据 - 空字符串日期
        MoeBusinessBookOnline bookOnline = createBookOnline(BookOnlineDTO.UseVersion.OB_3_0);
        BookOnlineSubmitParams obParams = createSubmitParams(
                "",           // 空字符串日期
                null,         // 没有staffId
                true,         // noStartTime = true
                createPetDataWithoutStaff()
        );

        // 执行测试
        boolean result = obGroomingService.canAutoAssign(bookOnline, obParams);

        // 验证结果
        assertFalse(result);
    }

    @Test
    public void testCanAutoAssign_BlankAppointmentDate_ReturnsFalse() {
        // 准备测试数据 - 空白字符串日期
        MoeBusinessBookOnline bookOnline = createBookOnline(BookOnlineDTO.UseVersion.OB_3_0);
        BookOnlineSubmitParams obParams = createSubmitParams(
                "   ",        // 空白字符串日期
                null,         // 没有staffId
                true,         // noStartTime = true
                createPetDataWithoutStaff()
        );

        // 执行测试
        boolean result = obGroomingService.canAutoAssign(bookOnline, obParams);

        // 验证结果
        assertFalse(result);
    }

    @Test
    public void testCanAutoAssign_HasStaffIdAndHasStartTime_ReturnsFalse() {
        // 准备测试数据 - 有staffId且有开始时间
        MoeBusinessBookOnline bookOnline = createBookOnline(BookOnlineDTO.UseVersion.OB_3_0);
        BookOnlineSubmitParams obParams = createSubmitParams(
                "2025-01-15", // 有日期
                123,          // 有staffId
                false,        // noStartTime = false (有开始时间)
                createPetDataWithoutStaff()
        );

        // 执行测试
        boolean result = obGroomingService.canAutoAssign(bookOnline, obParams);

        // 验证结果
        assertFalse(result);
    }

    @Test
    public void testCanAutoAssign_HasStaffIdButNoStartTime_ReturnsTrue() {
        // 准备测试数据 - 有staffId但没有开始时间
        MoeBusinessBookOnline bookOnline = createBookOnline(BookOnlineDTO.UseVersion.OB_3_0);
        BookOnlineSubmitParams obParams = createSubmitParams(
                "2025-01-15", // 有日期
                123,          // 有staffId
                true,         // noStartTime = true (没有开始时间)
                createPetDataWithoutStaff()
        );

        // 执行测试
        boolean result = obGroomingService.canAutoAssign(bookOnline, obParams);

        // 验证结果
        assertTrue(result);
    }

    @Test
    public void testCanAutoAssign_PetDataHasStaffAndHasStartTime_ReturnsFalse() {
        // 准备测试数据 - petData中有staffId且有开始时间
        MoeBusinessBookOnline bookOnline = createBookOnline(BookOnlineDTO.UseVersion.OB_3_0);
        BookOnlineSubmitParams obParams = createSubmitParams(
                "2025-01-15", // 有日期
                null,         // 没有主staffId
                false,        // noStartTime = false (有开始时间)
                createPetDataWithStaff(456) // petData中有staffId
        );

        // 执行测试
        boolean result = obGroomingService.canAutoAssign(bookOnline, obParams);

        // 验证结果
        assertFalse(result);
    }

    @Test
    public void testCanAutoAssign_PetDataHasStaffButNoStartTime_ReturnsTrue() {
        // 准备测试数据 - petData中有staffId但没有开始时间
        MoeBusinessBookOnline bookOnline = createBookOnline(BookOnlineDTO.UseVersion.OB_3_0);
        BookOnlineSubmitParams obParams = createSubmitParams(
                "2025-01-15", // 有日期
                null,         // 没有主staffId
                true,         // noStartTime = true (没有开始时间)
                createPetDataWithStaff(456) // petData中有staffId
        );

        // 执行测试
        boolean result = obGroomingService.canAutoAssign(bookOnline, obParams);

        // 验证结果
        assertTrue(result);
    }

    @Test
    public void testCanAutoAssign_NotOB3Version_ReturnsFalse() {
        // 准备测试数据 - 不是OB 3.0版本
        MoeBusinessBookOnline bookOnline = createBookOnline(BookOnlineDTO.UseVersion.OB_2_0);
        BookOnlineSubmitParams obParams = createSubmitParams(
                "2025-01-15", // 有日期
                null,         // 没有staffId
                true,         // noStartTime = true
                createPetDataWithoutStaff()
        );

        // 执行测试
        boolean result = obGroomingService.canAutoAssign(bookOnline, obParams);

        // 验证结果
        assertFalse(result);
    }

    @Test
    public void testCanAutoAssign_NullUseVersion_ReturnsFalse() {
        // 准备测试数据 - useVersion为null
        MoeBusinessBookOnline bookOnline = createBookOnline(null);
        BookOnlineSubmitParams obParams = createSubmitParams(
                "2025-01-15", // 有日期
                null,         // 没有staffId
                true,         // noStartTime = true
                createPetDataWithoutStaff()
        );

        // 执行测试
        boolean result = obGroomingService.canAutoAssign(bookOnline, obParams);

        // 验证结果
        assertFalse(result);
    }

    @Test
    public void testCanAutoAssign_MultiplePetsWithMixedStaff_ReturnsTrue() {
        // 准备测试数据 - 多个宠物，部分有staffId，部分没有
        MoeBusinessBookOnline bookOnline = createBookOnline(BookOnlineDTO.UseVersion.OB_3_0);
        
        List<BookOnlinePetParams> petData = new ArrayList<>();
        petData.add(createPetWithStaff(123));    // 有staffId
        petData.add(createPetWithoutStaff());    // 没有staffId
        petData.add(createPetWithStaff(456));    // 有staffId
        
        BookOnlineSubmitParams obParams = createSubmitParams(
                "2025-01-15", // 有日期
                null,         // 没有主staffId
                true,         // noStartTime = true
                petData
        );

        // 执行测试
        boolean result = obGroomingService.canAutoAssign(bookOnline, obParams);

        // 验证结果 - 因为petData中有staffId，所以hasNoStaff为false，但noStartTime为true，所以返回true
        assertTrue(result);
    }

    @Test
    public void testCanAutoAssign_EmptyPetData_ReturnsTrue() {
        // 准备测试数据 - 空的petData列表
        MoeBusinessBookOnline bookOnline = createBookOnline(BookOnlineDTO.UseVersion.OB_3_0);
        BookOnlineSubmitParams obParams = createSubmitParams(
                "2025-01-15", // 有日期
                null,         // 没有staffId
                true,         // noStartTime = true
                new ArrayList<>() // 空的petData
        );

        // 执行测试
        boolean result = obGroomingService.canAutoAssign(bookOnline, obParams);

        // 验证结果
        assertTrue(result);
    }

    // ========== 辅助方法 ==========

    /**
     * 创建 MoeBusinessBookOnline 测试对象
     */
    private MoeBusinessBookOnline createBookOnline(Byte useVersion) {
        MoeBusinessBookOnline bookOnline = new MoeBusinessBookOnline();
        bookOnline.setUseVersion(useVersion);
        return bookOnline;
    }

    /**
     * 创建 BookOnlineSubmitParams 测试对象
     */
    private BookOnlineSubmitParams createSubmitParams(String appointmentDate, Integer staffId, 
                                                     boolean noStartTime, List<BookOnlinePetParams> petData) {
        BookOnlineSubmitParams params = new BookOnlineSubmitParams();
        params.setAppointmentDate(appointmentDate);
        params.setStaffId(staffId);
        params.setNoStartTime(noStartTime);
        params.setPetData(petData);
        return params;
    }

    /**
     * 创建没有staffId的petData列表
     */
    private List<BookOnlinePetParams> createPetDataWithoutStaff() {
        List<BookOnlinePetParams> petData = new ArrayList<>();
        petData.add(createPetWithoutStaff());
        return petData;
    }

    /**
     * 创建有staffId的petData列表
     */
    private List<BookOnlinePetParams> createPetDataWithStaff(Integer staffId) {
        List<BookOnlinePetParams> petData = new ArrayList<>();
        petData.add(createPetWithStaff(staffId));
        return petData;
    }

    /**
     * 创建没有staffId的宠物参数
     */
    private BookOnlinePetParams createPetWithoutStaff() {
        BookOnlinePetParams pet = new BookOnlinePetParams();
        pet.setPetName("TestPet");
        pet.setBreed("TestBreed");
        pet.setStaffId(null); // 没有staffId
        return pet;
    }

    /**
     * 创建有staffId的宠物参数
     */
    private BookOnlinePetParams createPetWithStaff(Integer staffId) {
        BookOnlinePetParams pet = new BookOnlinePetParams();
        pet.setPetName("TestPet");
        pet.setBreed("TestBreed");
        pet.setStaffId(staffId); // 有staffId
        return pet;
    }
}
