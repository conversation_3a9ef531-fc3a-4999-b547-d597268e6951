package com.moego.server.grooming.server;

import com.moego.common.enums.BooleanEnum;
import com.moego.server.grooming.api.IOnlineBookingServiceBase;
import com.moego.server.grooming.dto.PreAuthAmountDTO;
import com.moego.server.grooming.dto.PrepayAmountDTO;
import com.moego.server.grooming.dto.ob.AvailableStaffDTO;
import com.moego.server.grooming.dto.ob.OBServiceDTO;
import com.moego.server.grooming.dto.ob.OBServiceListDto;
import com.moego.server.grooming.dto.ob.OBTimeSlotDTO;
import com.moego.server.grooming.dto.ob.ServiceOBSettingDTO;
import com.moego.server.grooming.dto.ob.StaffAvailableDateDTO;
import com.moego.server.grooming.dto.ob.StaffFirstAvailableDateDTO;
import com.moego.server.grooming.mapper.MoeBusinessBookOnlineMapper;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.params.PreAuthAmountParams;
import com.moego.server.grooming.params.PrepayAmountParams;
import com.moego.server.grooming.params.ob.AvailableStaffParams;
import com.moego.server.grooming.params.ob.ServiceOBSettingQueryParams;
import com.moego.server.grooming.service.CompanyGroomingServiceQueryService;
import com.moego.server.grooming.service.MoeGroomingBookOnlineService;
import com.moego.server.grooming.service.dto.OBAvailableTimeDto;
import com.moego.server.grooming.service.ob.OBBusinessStaffService;
import com.moego.server.grooming.service.ob.OBBusinessStaffTimeService;
import com.moego.server.grooming.service.ob.OBClientTimeSlotService;
import com.moego.server.grooming.service.ob.OBGroomingService;
import com.moego.server.grooming.service.ob.OBServiceService;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2023/10/8
 */
@RestController
@RequiredArgsConstructor
public class OnlineBookingServer extends IOnlineBookingServiceBase {

    private final OBServiceService obServiceService;
    private final OBBusinessStaffService obBusinessStaffService;
    private final OBBusinessStaffTimeService obBusinessStaffTimeService;
    private final OBClientTimeSlotService obClientTimeSlotService;
    private final MoeGroomingBookOnlineService bookOnlineService;
    private final MoeBusinessBookOnlineMapper bookOnlineMapper;
    private final CompanyGroomingServiceQueryService companyGroomingServiceQueryService;
    private final OBGroomingService obGroomingService;

    @Override
    public OBServiceListDto getAvailableServiceList(OBServiceDTO params) {
        return obServiceService.getPetServiceList(params);
    }

    @Override
    public AvailableStaffDTO getAvailableStaffList(AvailableStaffParams params) {
        return obBusinessStaffService.getAvailableStaffList(params);
    }

    @Override
    public List<StaffFirstAvailableDateDTO> getStaffFirstAvailableDateList(OBTimeSlotDTO params) {
        params.setQueryCountPerStaff(1);
        return obBusinessStaffTimeService.getStaffFirstAvailableDateNew(params);
    }

    @Override
    public List<StaffAvailableDateDTO> getStaffAvailableDateList(OBTimeSlotDTO params) {
        Map<String, OBAvailableTimeDto> map =
                obClientTimeSlotService.getTimeSlotListV2(params).getAvailableDateTimes();
        return map.entrySet().stream()
                .map(entry -> StaffAvailableDateDTO.builder()
                        .date(entry.getKey())
                        .isAm(BooleanUtils.isTrue(entry.getValue().getAvailable()[0]))
                        .isPm(BooleanUtils.isTrue(entry.getValue().getAvailable()[1]))
                        .staffList(Optional.ofNullable(entry.getValue().getStaffList())
                                .map(staffList -> staffList.stream()
                                        .map(staff -> StaffAvailableDateDTO.StaffAvailableTimeSlotDTO.builder()
                                                .staffId(staff.getId())
                                                .firstName(staff.getFirstName())
                                                .lastName(staff.getLastName())
                                                .amTimeslots(Optional.ofNullable(staff.getAvailableTime()
                                                                .getAm())
                                                        .map(List::copyOf)
                                                        .orElse(List.of()))
                                                .pmTimeslots(Optional.ofNullable(staff.getAvailableTime()
                                                                .getPm())
                                                        .map(List::copyOf)
                                                        .orElse(List.of()))
                                                .build())
                                        .toList())
                                .orElse(List.of()))
                        .build())
                .toList();
    }

    @Override
    public PrepayAmountDTO getPrepayAmount(Integer businessId, PrepayAmountParams params) {
        MoeBusinessBookOnline bookOnline = bookOnlineMapper.selectByBusinessId(businessId);
        params.setPrepayType(bookOnline.getPrepayType());
        params.setDepositType(bookOnline.getDepositType());
        return bookOnlineService.calculateOBPrepayAmount(businessId, params);
    }

    @Override
    public PrepayAmountDTO getPrepayAmountByGuid(Integer businessId, String guid) {
        return bookOnlineService.getOBPrepayAmount(businessId, guid);
    }

    @Override
    public PreAuthAmountDTO getPreAuthAmount(Integer businessId, PreAuthAmountParams params) {
        return bookOnlineService.calculateOBPreAuthAmount(businessId, params);
    }

    @Override
    public Map<Long, ServiceOBSettingDTO> getServiceOBSetting(ServiceOBSettingQueryParams params) {
        var bookOnlineServiceMap = companyGroomingServiceQueryService.obServiceQueryByIds(
                params.companyId(), params.businessId().intValue(), params.serviceIdList());
        return bookOnlineServiceMap.entrySet().stream()
                .map(entry -> Map.entry(
                        entry.getKey().longValue(),
                        new ServiceOBSettingDTO(
                                entry.getValue().getBookOnlineAvailable().equals(BooleanEnum.VALUE_TRUE),
                                entry.getValue().getShowBasePrice(),
                                entry.getValue().getIsAllStaff().equals(BooleanEnum.VALUE_TRUE),
                                entry.getValue().getAllowBookingWithOtherCareType())))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    @Override
    public boolean triggerBookingRequestAutoAccepted(long appointmentId) {
        return obGroomingService.triggerBookingRequestAutoAccepted(Math.toIntExact(appointmentId));
    }
}
