package com.moego.server.grooming.service.ob;

import static com.moego.server.grooming.service.utils.SmartScheduleUtil.convertTimeSlotList;
import static com.moego.server.grooming.service.utils.SmartScheduleUtil.fillServiceAddressV2;
import static com.moego.server.grooming.service.utils.SmartScheduleUtil.filterNonWorkingTime;
import static com.moego.server.grooming.service.utils.SmartScheduleUtil.isInClosedDate;

import com.moego.common.constant.CommonConstant;
import com.moego.common.dto.BusinessPreferenceDto;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.utils.DateUtil;
import com.moego.common.utils.PrimitiveTypeUtil;
import com.moego.common.utils.WeekUtil;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.client.IBusinessClosedDateClient;
import com.moego.server.business.client.IBusinessServiceAreaClient;
import com.moego.server.business.client.IBusinessSmartSchedulingClient;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.AppointmentPetIdDTO;
import com.moego.server.business.dto.ParsedCloseDate;
import com.moego.server.business.dto.ServiceAreaSettingDTO;
import com.moego.server.business.dto.SmartScheduleDrivingRuleDTO;
import com.moego.server.business.dto.StaffSmartScheduleSettingDTO;
import com.moego.server.business.dto.StaffTime;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.business.params.GetSmartScheduleSettingParams;
import com.moego.server.business.params.ServiceAreaInsideBatchRequestV2;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.dto.CustomerAddressDto;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.grooming.config.SmartScheduleConfiguration;
import com.moego.server.grooming.dto.LimitGroupDTO;
import com.moego.server.grooming.dto.SmartScheduleGroomingDetailsDTO;
import com.moego.server.grooming.dto.ss.SmartScheduleResultDto;
import com.moego.server.grooming.dto.ss.SmartScheduleStaffMap;
import com.moego.server.grooming.dto.ss.SmartScheduleVO;
import com.moego.server.grooming.service.GoogleMapService;
import com.moego.server.grooming.service.OnlineBookingService;
import com.moego.server.grooming.service.SmartScheduleService;
import com.moego.server.grooming.service.StaffTimeSyncService;
import com.moego.server.grooming.service.TimeSlot;
import com.moego.server.grooming.service.dto.ob.OBPetLimitFilterDTO;
import com.moego.server.grooming.service.dto.ob.SmartScheduleV2DTO;
import com.moego.server.grooming.service.utils.SmartScheduleUtil;
import com.moego.server.grooming.web.vo.SmartScheduleParam;
import com.moego.server.grooming.web.vo.ob.SmartScheduleV2Request;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TreeMap;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class SmartScheduleV2Service {

    private final GoogleMapService googleMapService;
    private final OnlineBookingService onlineBookingService;
    private final SmartScheduleService smartScheduleService;

    private final IBusinessClosedDateClient iBusinessClosedDateClient;
    private final IBusinessStaffClient iBusinessStaffClient;
    private final IBusinessServiceAreaClient iBusinessServiceAreaClient;
    private final IBusinessSmartSchedulingClient iBusinessSmartSchedulingClient;
    private final IBusinessBusinessClient iBusinessBusinessClient;
    private final OBAddressService obAddressService;
    private final ICustomerCustomerClient iCustomerCustomerClient;
    private final OBPetLimitService obPetLimitService;
    private final StaffTimeSyncService staffTimeSyncService;

    /* buffer (extra) time for schedule a new appointment */

    @Autowired
    @Qualifier(value = SmartScheduleConfiguration.SMART_SCHEDULE_EXECUTOR_SERVICE)
    private ExecutorService executorService;

    public SmartScheduleResultDto smartScheduleV2(Integer businessId, SmartScheduleV2Request request) {
        var staffIds = request.getStaffIds();

        // Check lat / lng when ss is enabled
        if (BooleanUtils.isFalse(request.getDisableSmartScheduling())
                && !StringUtils.hasText(request.getAddressLat())
                && !StringUtils.hasText(request.getAddressLng())) {
            log.error(
                    "OB 3.0 lat[{}] / lng[{}] must be non-null when ss is enabled",
                    request.getAddressLat(),
                    request.getAddressLng());
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "lat / lng must be non-null when ss is enabled");
        }
        SmartScheduleResultDto resultDto = new SmartScheduleResultDto();
        Map<String, SmartScheduleStaffMap> resultDayMap = new HashMap<>();
        resultDto.setDayMap(resultDayMap);

        // 获取 staff ss setting map
        Map<Integer, StaffSmartScheduleSettingDTO> staffSsSettingMap =
                iBusinessSmartSchedulingClient.getStaffSmartScheduleSettingMap(
                        new GetSmartScheduleSettingParams(businessId, null));
        SmartScheduleParam requestParam = new SmartScheduleParam();
        requestParam.setBusinessId(businessId);
        requestParam.setStaffSsSettingMap(staffSsSettingMap);
        BeanUtils.copyProperties(request, requestParam);
        if (request.isFromOB()) {
            // <dayOfWeek, <staffId, staffTime>>
            var staffAvailableWeekTime = onlineBookingService.getStaffTimeByBusinessIdAndStaffIds(businessId, staffIds);
            // 对 weekStaffWorkingHours取交集
            requestParam.setObStaffTime(staffAvailableWeekTime);
        } else {
            requestParam.setCheckCACD(checkCACD(businessId));
        }
        if (requestParam.isCheckCACD()) {
            if (!StringUtils.hasText(requestParam.getAddressZipcode())
                    && (StringUtils.hasText(requestParam.getAddressLat())
                            && StringUtils.hasText(requestParam.getAddressLng()))) {
                requestParam.setAddressZipcode(smartScheduleService.getZipcodeByLatLng(
                        requestParam.getAddressLat(), requestParam.getAddressLng()));
            }
        }

        BusinessPreferenceDto businessPreference = iBusinessBusinessClient.getBusinessPreference(businessId);

        var companyId =
                iBusinessBusinessClient.getCompanyIdByBusinessId(businessId).companyId();

        // var shiftManagementStaffTimeMap =
        //     staffTimeSyncService.queryShiftManagementStaffTime(businessId, companyId, staffIds);
        // requestParam.setStaffAvailabilityMap(shiftManagementStaffTimeMap);
        // var shiftManagementOverrideStaffTime =
        //     staffTimeSyncService.queryShiftManagementOverrideStaffTime(businessId, companyId, staffIds);
        // requestParam.setStaffOverridateMap(shiftManagementOverrideStaffTime);

        // 获取商家一段时间内员工的 override date 设置
        // Map<Integer, List<String>> staffOverrideDateList = shiftManagementOverrideStaffTime.entrySet()
        //     .stream()
        //     .collect(Collectors.toMap(Map.Entry::getKey, e ->
        // e.getValue().stream().map(TimeAvailabilityDay::getOverrideDate).toList()));
        // requestParam.setStaffOverrideDateList(staffOverrideDateList);

        Map<String, Map<Integer, StaffTime>> staffWorkingHour = staffTimeSyncService.getStaffTimeMap(
                new HashSet<>(staffIds),
                request.getDate(),
                LocalDate.parse(request.getDate())
                        .plusDays(request.getFarthestDay())
                        .toString(),
                LocalDate.now(ZoneId.of(businessPreference.getTimezoneName())),
                DateUtil.getNowMinutes(businessPreference.getTimezoneName()),
                businessId,
                companyId);
        requestParam.setStaffTime(staffWorkingHour);

        // // 获取商家所有staff的week daily hours
        // Map<Integer, Map<LocalDate, List<TimeRangeDto>>> dateStaffWorkingHour =
        //         iBusinessStaffClient.getStaffWorkingHourWithOverrideDate(
        //                 businessId,
        //                 staffIds,
        //                 request.getDate(),
        //                 LocalDate.parse(request.getDate())
        //                         .plusDays(request.getFarthestDay())
        //                         .toString());
        // requestParam.setStaffDateWorkingHours(dateStaffWorkingHour);
        // // 获取商家一段时间内员工的 override date 设置
        // Map<Integer, List<String>> staffOverrideDateList = iBusinessStaffClient.getStaffOverrideDateList(
        //         businessId,
        //         staffIds,
        //         request.getDate(),
        //         LocalDate.parse(request.getDate())
        //                 .plusDays(request.getFarthestDay())
        //                 .toString());
        // requestParam.setStaffOverrideDateList(staffOverrideDateList);

        // 排除closed date
        List<ParsedCloseDate> allClosedDate = iBusinessClosedDateClient.getAllCloseDate(businessId);
        requestParam.setAllClosedDate(allClosedDate);
        // set startDate
        requestParam.setStartDate(LocalDate.parse(request.getDate()));
        // generate pet limit
        OBPetLimitFilterDTO obPetLimitFilterDTO = obPetLimitService.generateWithCurrentAppointment(
                companyId, businessId, request.getPetParamList(), request.getServiceIds());
        requestParam.setObPetLimitFilterDTO(obPetLimitFilterDTO);

        // 缓存一次ss的 customerAddresses
        Map<Integer, CustomerAddressDto> customerAddress = new HashMap<>();
        int STEP = Runtime.getRuntime().availableProcessors() * 2; // 步长： 一次查询次数
        SmartScheduleStaffMap[] resultArray = new SmartScheduleStaffMap[request.getFarthestDay()];
        // initialize params
        SmartScheduleV2DTO smartScheduleDTO = new SmartScheduleV2DTO()
                .setResultArray(resultArray)
                .setCustomerAddressDtoMap(customerAddress)
                .setRequest(requestParam);
        // set preferred day and time
        if (Objects.nonNull(request.getApplyClientPreferenceCustomerId())) {
            MoeBusinessCustomerDTO customerDTO =
                    iCustomerCustomerClient.getCustomerWithDeleted(request.getApplyClientPreferenceCustomerId());
            if (customerDTO == null || !Objects.equals(customerDTO.getBusinessId(), businessId)) {
                throw new CommonException(ResponseCodeEnum.PARAMS_ERROR);
            }
            smartScheduleDTO.setIsApplyClientPreference(Boolean.TRUE);
            smartScheduleDTO.setPreferredDay(customerDTO.getPreferredDay());
            smartScheduleDTO.setPreferredTime(customerDTO.getPreferredTime());
        }
        int firstSlotIndex = -1;
        int remainCount = Objects.nonNull(request.getCount()) ? request.getCount() : 31;
        // initialize remain count until end of the month
        if (BooleanUtils.isTrue(request.getQueryEndOfTheMonth())) {
            remainCount = getRemainCount(request.getDate(), 0);
        }

        // timezone
        smartScheduleDTO.setTimezoneName(businessPreference.getTimezoneName());
        // Limit query time
        long start = System.currentTimeMillis();
        for (int index = 0; index < request.getFarthestDay() + STEP; index += STEP) {
            // dynamically calculate STEP, endIndex value
            STEP = Math.min(remainCount, STEP);
            int endIndex = index + STEP;
            smartScheduleOpt(smartScheduleDTO.setStartIndex(index).setEndIndex(endIndex));
            // Over limit days seconds
            if (Objects.nonNull(request.getQueryLimitDays())
                    && index + 1 > request.getQueryLimitDays()
                    && Objects.nonNull(request.getOverLimitDaysSeconds())
                    && System.currentTimeMillis() - start > request.getOverLimitDaysSeconds() * 1000) {
                log.error("OB 3.0 smart schedule over limit days and over 10 seconds");
                return resultDto;
            }
            for (int j = 0; j < STEP && j + index < resultArray.length; j++) {
                if (resultArray[j + index] != null) {
                    if (!resultDto.isAvailable()) {
                        firstSlotIndex = j + index;
                        resultDto.setAvailable(true);
                        // Dynamically calculate the remaining days to be queried
                        if (BooleanUtils.isTrue(request.getQueryEndOfTheMonth())) {
                            request.setCount(getRemainCount(request.getDate(), firstSlotIndex));
                        }
                    }
                    SmartScheduleStaffMap availableSlot = resultArray[j + index];
                    resultDayMap.put(availableSlot.getDayStr(), availableSlot);

                    if (Objects.nonNull(request.getQueryCountPerStaff()) && request.getQueryCountPerStaff() > 0) {
                        var needQueryStaffList =
                                getNeedQueryStaffList(staffIds, resultDto.getDayMap(), request.getQueryCountPerStaff());
                        if (!needQueryStaffList.isEmpty()) {
                            requestParam.setStaffIds(needQueryStaffList);
                            // 重新生成 smartScheduleDTO
                            smartScheduleDTO.setRequest(requestParam);
                            break;
                        }
                    }

                    remainCount = request.getCount() - (j + index - firstSlotIndex + 1);
                    if (remainCount <= 0) {
                        // ss each staff each time slot
                        smartScheduleFullyDay(smartScheduleDTO, firstSlotIndex, resultDayMap);
                        // find enough slots
                        return resultDto;
                    }
                }
            }
        }
        // ss each staff each time slot
        smartScheduleFullyDay(smartScheduleDTO, firstSlotIndex, resultDayMap);
        return resultDto;
    }

    @Nonnull
    private static List<Integer> getNeedQueryStaffList(
            final Collection<Integer> staffIds, Map<String, SmartScheduleStaffMap> dayMap, Integer queryCountPerStaff) {
        var staffCountMap = dayMap.values().stream()
                .map(SmartScheduleStaffMap::getStaffMap)
                .map(Map::values)
                .flatMap(Collection::stream)
                .filter(vo -> !CollectionUtils.isEmpty(vo.getAvailableRange()))
                .collect(Collectors.groupingBy(SmartScheduleVO::getStaffId, Collectors.counting()));
        return staffIds.stream()
                .filter(staffId -> staffCountMap.getOrDefault(staffId, 0L) < queryCountPerStaff)
                .toList();
    }

    /**
     * 获取第一个可用天截止到月底的剩余天数
     *
     * @param date           开始日期
     * @param firstSlotIndex 当天到第一个可用天偏移量
     * @return 剩余天数
     */
    public static int getRemainCount(String date, int firstSlotIndex) {
        LocalDate startDate = LocalDate.parse(date).plusDays(firstSlotIndex);
        return startDate.lengthOfMonth() - startDate.getDayOfMonth() + 1;
    }

    public void smartScheduleFullyDay(
            SmartScheduleV2DTO smartScheduleDTO, int firstSlotIndex, Map<String, SmartScheduleStaffMap> resultDayMap) {
        SmartScheduleParam requestParam = smartScheduleDTO.getRequest();
        boolean notFirstAvailableDay =
                BooleanUtils.isNotTrue(requestParam.getQueryPerHalfDay()) || firstSlotIndex == -1;
        if (notFirstAvailableDay) {
            return;
        }
        requestParam.setQueryPerHalfDay(false);
        smartScheduleOpt(smartScheduleDTO.setStartIndex(firstSlotIndex).setEndIndex(firstSlotIndex + 1));
        if (firstSlotIndex >= smartScheduleDTO.getResultArray().length) {
            return;
        }
        SmartScheduleStaffMap availableSlot = smartScheduleDTO.getResultArray()[firstSlotIndex];
        resultDayMap.put(availableSlot.getDayStr(), availableSlot);
    }

    private static Map<String, List<SmartScheduleGroomingDetailsDTO>> listToMapByDate(
            List<SmartScheduleGroomingDetailsDTO> allServices, Integer filterGroomingId) {
        Map<String, List<SmartScheduleGroomingDetailsDTO>> result = new TreeMap<>();
        allServices.forEach(srv -> {
            if (!PrimitiveTypeUtil.isNullOrZero(filterGroomingId)
                    && srv.getGroomingId().equals(filterGroomingId)) {
                return;
            }
            // 兼容旧逻辑 这里取service date更准确
            List<SmartScheduleGroomingDetailsDTO> list = result.computeIfAbsent(
                    StringUtils.hasText(srv.getStartDate()) ? srv.getStartDate() : srv.getAppointmentDate(),
                    k -> new LinkedList<>());
            list.add(srv);
        });
        return result;
    }

    private void getMoreCustomerAddresses(
            Map<Integer, CustomerAddressDto> customerAddressDtoMap,
            List<SmartScheduleGroomingDetailsDTO> servicesOfAllDates) {
        List<Integer> customerIds = servicesOfAllDates.stream()
                .map(SmartScheduleGroomingDetailsDTO::getCustomerId)
                .filter(id -> id > 0) /* skip dummy customer for block appointment */
                .filter(id -> !customerAddressDtoMap.containsKey(id))
                .distinct()
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(customerIds)) {
            customerAddressDtoMap.putAll(obAddressService.batchGetPrimaryAddress(customerIds));
        }
    }

    /**
     * process [startIndex, endIndex) 左闭右开
     *
     * @param smartScheduleDTO
     */
    private void smartScheduleOpt(SmartScheduleV2DTO smartScheduleDTO) {
        SmartScheduleStaffMap[] resultArray = smartScheduleDTO.getResultArray();
        Map<Integer, CustomerAddressDto> customerAddressDtoMap = smartScheduleDTO.getCustomerAddressDtoMap();
        SmartScheduleParam request = smartScheduleDTO.getRequest();
        final int startIndex = smartScheduleDTO.getStartIndex();
        final int endIndex = smartScheduleDTO.getEndIndex();

        if (startIndex >= Math.min(endIndex, resultArray.length)) {
            return;
        }
        // check CACD
        Map<String, Map<Integer, Boolean>> dateToInside;
        if (request.isCheckCACD()) {
            String startDate = request.getStartDate().plusDays(startIndex).toString();
            String endDate = request.getStartDate().plusDays(endIndex).toString();
            dateToInside = getCACDDateAvailableMap(request.getBusinessId(), startDate, endDate, request);
        } else {
            dateToInside = Collections.emptyMap();
        }

        boolean isSmart = !Boolean.TRUE.equals(request.getDisableSmartScheduling());
        // 获取指定日期的所有staff的all kinds of grooming  一次性获取所有appt
        LocalDate firstDay = request.getStartDate().plusDays(startIndex);
        LocalDate lastDay = request.getStartDate().plusDays(Math.min(endIndex, resultArray.length));

        // 查询 staff 指定日期范围内的所有 PetDetail
        List<SmartScheduleGroomingDetailsDTO> servicesOfAllDates = smartScheduleService.queryByBusinessIdBetweenDates(
                request.getBusinessId(), firstDay, lastDay, request.getStaffIds(), smartScheduleDTO.getTimezoneName());

        Map<String, List<SmartScheduleGroomingDetailsDTO>> dayToServicesMap =
                listToMapByDate(servicesOfAllDates, request.getFilterGroomingId());

        List<AppointmentPetIdDTO> petIdDTOList = servicesOfAllDates.stream()
                .filter(dto -> !Objects.equals(dto.getGroomingId(), request.getFilterGroomingId()))
                .map(dto -> AppointmentPetIdDTO.builder()
                        .petId(dto.getPetId())
                        .staffId(dto.getStaffId())
                        .appointmentDate(dto.getAppointmentDate())
                        .appointmentId(dto.getGroomingId())
                        .serviceIds(List.of(dto.getServiceId()))
                        .build())
                .toList();

        OBPetLimitFilterDTO obPetLimitFilterDTO =
                obPetLimitService.fillWithExistingAppointment(request.getObPetLimitFilterDTO(), petIdDTOList);
        request.setObPetLimitFilterDTO(obPetLimitFilterDTO);

        // 补充新的 customerAddresses
        if (isSmart) {
            getMoreCustomerAddresses(customerAddressDtoMap, servicesOfAllDates);
            request.setCustomerAddressDtoMap(customerAddressDtoMap);
        }
        Map<Integer, List<CompletableFuture<SmartScheduleVO>>> futureMap = new HashMap<>();
        for (int currentDay = startIndex; currentDay < endIndex && currentDay < resultArray.length; currentDay++) {
            LocalDate currLocalDate = request.getStartDate().plusDays(currentDay);
            // check closed date
            if (isInClosedDate(currLocalDate, request.getAllClosedDate())
                    ||
                    // filter preferred day
                    (BooleanUtils.isTrue(smartScheduleDTO.getIsApplyClientPreference())
                            && !isPreferredDay(currLocalDate, smartScheduleDTO.getPreferredDay()))) {
                log.info(
                        "OB 3.0 the business: [{}], currentDate: [{}] closed or not client preferred day",
                        request.getBusinessId(),
                        currLocalDate);
                continue;
            }
            String currLocalDateStr = currLocalDate.toString();
            List<CompletableFuture<SmartScheduleVO>> futures = request.getStaffIds().stream()
                    .map(staffId -> CompletableFuture.supplyAsync(
                            () -> {
                                // check cacd
                                if (!CollectionUtils.isEmpty(dateToInside)) {
                                    Map<Integer, Boolean> staffFlagMap = dateToInside.get(currLocalDateStr);
                                    if (staffFlagMap != null && Boolean.FALSE.equals(staffFlagMap.get(staffId))) {
                                        log.info(
                                                "skip staff cacd check false for {} staff {}",
                                                currLocalDateStr,
                                                staffId);
                                        return null;
                                    }
                                }
                                // 计算当天某个staff的可用时间段
                                List<TimeSlot> freeTimeSlots =
                                        getStaffCurDateFreeTimeSlots(currLocalDate, staffId, dayToServicesMap, request);
                                // filter preferred time
                                filterTimeByPreference(
                                        freeTimeSlots,
                                        smartScheduleDTO.getPreferredTime(),
                                        SmartScheduleService.getStaffServiceDuration(
                                                staffId.longValue(),
                                                request.getServiceDuration(),
                                                request.getStaffServiceDuration()));
                                // filter today time slot
                                if (currLocalDate.isEqual(
                                        LocalDate.now(ZoneId.of(smartScheduleDTO.getTimezoneName())))) {
                                    int nowMinutes = DateUtil.getNowMinutes(smartScheduleDTO.getTimezoneName());
                                    freeTimeSlots = filterTodayTimeSlot(nowMinutes, freeTimeSlots);
                                    log.info(
                                            "OB 3.0 SS before the business's available time of the day filter result: [{}]",
                                            freeTimeSlots);
                                }
                                List<TimeSlot> availableTimeSlots =
                                        smartSchedulingOneDay(staffId, freeTimeSlots, request, currLocalDateStr);
                                if (!availableTimeSlots.isEmpty()) {
                                    updateTimeWithDrivingTime(availableTimeSlots);
                                    checkFirstOrLastAppt(availableTimeSlots);
                                    return SmartScheduleVO.builder()
                                            .availableRange(convertTimeSlotList(availableTimeSlots))
                                            .date(currLocalDateStr)
                                            .staffId(staffId)
                                            .build();
                                }
                                return null;
                            },
                            executorService))
                    .toList();
            futureMap.put(currentDay, futures);
        }
        futureMap.forEach((resultIndex, futures) -> {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            futures.stream()
                    .map(CompletableFuture::join)
                    .filter(Objects::nonNull)
                    .forEach(vo -> {
                        SmartScheduleStaffMap currentVo = resultArray[resultIndex];
                        if (currentVo == null) {
                            currentVo = new SmartScheduleStaffMap();
                            currentVo.setDayStr(vo.getDate());
                            currentVo.setStaffMap(new HashMap<>());
                            resultArray[resultIndex] = currentVo;
                        }
                        currentVo.getStaffMap().put(vo.getStaffId(), vo);
                    });
        });
    }

    public List<TimeSlot> filterTodayTimeSlot(int nowMinutes, List<TimeSlot> availableTimeSlots) {
        if (CollectionUtils.isEmpty(availableTimeSlots)) {
            return Collections.emptyList();
        }

        // filter today time
        if (nowMinutes <= availableTimeSlots.get(0).getStart()) {
            return availableTimeSlots;
        }

        int start = 0;
        for (; start < availableTimeSlots.size(); start++) {
            TimeSlot timeSlot = availableTimeSlots.get(start);
            if (nowMinutes <= timeSlot.getStart()) {
                return availableTimeSlots.subList(start, availableTimeSlots.size());
            }
            if (nowMinutes > timeSlot.getStart() && nowMinutes <= timeSlot.getEnd()) {
                timeSlot.setStart(nowMinutes);
                break;
            }
        }
        return availableTimeSlots.subList(start, availableTimeSlots.size());
    }

    public boolean isPreferredDay(LocalDate currLocalDate, Integer[] preferredDay) {
        if (ArrayUtils.isEmpty(preferredDay)) {
            return false;
        }
        int dayOfWeek = currLocalDate.getDayOfWeek().getValue();
        // Sunday convert 7 to 0
        if (dayOfWeek == 7) {
            dayOfWeek = 0;
        }
        return Arrays.asList(preferredDay).contains(dayOfWeek);
    }

    /**
     * 过滤时间
     * 1.timeslot与preferTime没有交集：过滤
     * 2.timeslot开始时间在preferTime范围内：保留
     * 3.timeslot开始时间大于preferTime开始时间，timeslot结束时间大于preferTime开始时间：
     * 更新timeslot的开始时间为preferTime的开始时间，判断新的时间范围是否满足serviceDuration，满足则保留，不满足则过滤
     *
     * @param timeslots
     * @param preferTime
     */
    public void filterTimeByPreference(List<TimeSlot> timeslots, Integer[] preferTime, Integer serviceDuration) {
        if (preferTime == null || preferTime.length < 2) {
            return;
        }
        Integer preferTimeStart = preferTime[0];
        Integer preferTimeEnd = preferTime[1];

        timeslots.removeIf(timeslot -> {
            int timeslotStart = timeslot.getStart();
            int timeslotEnd = timeslot.getEnd();
            if (timeslotStart >= preferTimeStart && timeslotStart <= preferTimeEnd) {
                return false;
            }
            if (timeslotStart < preferTimeStart && timeslotEnd > preferTimeStart) {
                if (timeslotEnd - preferTimeStart >= serviceDuration) {
                    timeslot.setStart(preferTimeStart);
                    return false;
                }
            }
            return true;
        });
    }

    private Map<String, Map<Integer, Boolean>> getCACDDateAvailableMap(
            Integer businessId, String startDate, String endDate, SmartScheduleParam request) {
        ServiceAreaInsideBatchRequestV2 cacdRequest = new ServiceAreaInsideBatchRequestV2();
        try {
            cacdRequest.setLat(Double.parseDouble(request.getAddressLat()));
        } catch (Exception e) {
            cacdRequest.setLat(0.0);
            log.error("parse lat failed: " + request.getAddressLat(), e);
        }
        try {
            cacdRequest.setLng(Double.parseDouble(request.getAddressLng()));
        } catch (Exception e) {
            cacdRequest.setLng(0.0);
            log.error("parse lng failed: " + request.getAddressLng(), e);
        }
        cacdRequest.setZipcode(request.getAddressZipcode());
        cacdRequest.setStaffIds(request.getStaffIds());
        cacdRequest.setStartDate(startDate);
        cacdRequest.setEndDate(endDate);
        return iBusinessStaffClient
                .isLocationInsideAreaBatchV2(businessId, cacdRequest)
                .getIsInsideMap();
    }

    /**
     * 通过SmartScheduling查询一天内的time slot
     *
     * @param staffId
     * @param request
     * @return
     */
    public List<TimeSlot> smartSchedulingOneDay(
            Integer staffId, List<TimeSlot> freeTimeSlots, SmartScheduleParam request, String currentDate) {
        StaffSmartScheduleSettingDTO staffSsSetting =
                request.getStaffSsSettingMap().get(staffId);
        boolean isSmart = !Boolean.TRUE.equals(request.getDisableSmartScheduling());

        // pre-filter by time of service serviceDuration + buffer
        // todo: add buffer time enable
        Integer bufferTime = Objects.nonNull(request.getBufferTime()) ? request.getBufferTime() : 0;
        Integer serviceDuration = SmartScheduleService.getStaffServiceDuration(
                staffId.longValue(), request.getServiceDuration(), request.getStaffServiceDuration());
        List<TimeSlot> step1Times = freeTimeSlots.stream()
                .filter(t ->
                        t.getEnd() - t.getStart() >= SmartScheduleUtil.getTotalDuration(t, serviceDuration, bufferTime))
                .collect(Collectors.toList());

        // if empty (no time meet pre-filter), continue to next staff
        if (step1Times.isEmpty()) {
            return Collections.emptyList();
        }

        List<TimeSlot> resultList = new ArrayList<>();
        if (BooleanUtils.isTrue(request.getQueryOne())) {
            // query first time slot by day
            queryOne(step1Times, staffId, request, bufferTime, isSmart, resultList);
        } else if (BooleanUtils.isTrue(request.getQueryPerHalfDay())) {
            // query first time slot by half day
            List<TimeSlot> amSlotList = new ArrayList<>();
            List<TimeSlot> pmSlotList = new ArrayList<>();
            for (TimeSlot slot : step1Times) {
                if (slot.getStart() < DateUtil.getMinsByHourMins(12, 0)) {
                    amSlotList.add(slot);
                } else {
                    pmSlotList.add(slot);
                }
            }
            if (BooleanUtils.isNotTrue(request.getAmTimeSlotMap().get(currentDate))) {
                boolean result = queryOne(amSlotList, staffId, request, bufferTime, isSmart, resultList);
                if (result) {
                    request.getAmTimeSlotMap().put(currentDate, true);
                }
            }
            if (BooleanUtils.isNotTrue(request.getPmTimeSlotMap().get(currentDate))) {
                boolean result = queryOne(pmSlotList, staffId, request, bufferTime, isSmart, resultList);
                if (result) {
                    request.getPmTimeSlotMap().put(currentDate, true);
                }
            }
        } else {
            resultList = step1Times;
            // 依次获取每个地址到新地址的driving time
            if (isSmart) {
                // googleMapService.fillDrivingDistancesAndTime(step1Times, request);
                googleMapService.calculateAvailableTime(
                        step1Times, request.getAddressLat(), request.getAddressLng(), serviceDuration, bufferTime);
                resultList = step1Times.stream()
                        // 剩余时间足够
                        .filter(t -> (0 <= t.getAvailableTime()))
                        // 开车时间合适
                        .filter(t -> meetMaxDrivingTimeAndDist(t, staffSsSetting))
                        .collect(Collectors.toList());
            }
        }

        // 根据bufferTime 偏移timeslot
        SmartScheduleUtil.timeSlotOffset(freeTimeSlots, bufferTime);

        return resultList;
    }

    /**
     * query first time slot by all time slot list
     *
     * @param slotList   time slot list
     * @param request    request params
     * @param bufferTime buffer time, default 10 mins
     * @param isSmart    ss switch
     * @param resultList time slot result
     * @return filter result
     */
    public boolean queryOne(
            List<TimeSlot> slotList,
            Integer staffId,
            SmartScheduleParam request,
            Integer bufferTime,
            boolean isSmart,
            List<TimeSlot> resultList) {
        StaffSmartScheduleSettingDTO staffSsSetting =
                request.getStaffSsSettingMap().get(staffId);
        for (TimeSlot slot : slotList) {
            Integer duration = SmartScheduleService.getStaffServiceDuration(
                    staffId.longValue(), request.getServiceDuration(), request.getStaffServiceDuration());
            slot.updateAvailableTime(SmartScheduleUtil.getTotalDuration(slot, duration, bufferTime));
            // 以单个timeslot去查询驾驶时间，满足条件则返回，不再往下查找
            if (isSmart) {
                googleMapService.calculateAvailableTime(
                        Collections.singletonList(slot),
                        request.getAddressLat(),
                        request.getAddressLng(),
                        duration,
                        bufferTime);
            }
            if (slot.getAvailableTime() >= 0 && meetMaxDrivingTimeAndDist(slot, staffSsSetting)) {
                resultList.add(slot);
                return true;
            }
        }
        return false;
    }

    /**
     * 根据当前日期的services，构建空闲时间的timeslot列表
     *
     * @param currLocalDate
     * @param staffId
     * @param dayToServicesMap
     * @param request
     * @return
     */
    private List<TimeSlot> getStaffCurDateFreeTimeSlots(
            LocalDate currLocalDate,
            Integer staffId,
            Map<String, List<SmartScheduleGroomingDetailsDTO>> dayToServicesMap,
            SmartScheduleParam request) {
        List<SmartScheduleGroomingDetailsDTO> targetDayTickets =
                dayToServicesMap.getOrDefault(currLocalDate.toString(), List.of());
        List<SmartScheduleGroomingDetailsDTO> staffServices = targetDayTickets.stream()
                .filter(s -> staffId.equals(s.getStaffId()))
                .collect(Collectors.toList());
        // 剔除已占用时间，并根据staffHour获取可用时间列表; ob直接使用obStaffTime不用考虑businessWorkingHours
        return buildFreeTimeSlotsByStaffId(staffServices, staffId, currLocalDate, request);
    }

    /**
     * 上闭下开：cond1： 1. time ok-> true  2. time not ok-> cond4-> false
     * 上闭下闭：
     * 上开下闭：
     * 上开下开：cond3->true
     *
     * @param t
     * @param setting
     * @return
     */
    private boolean meetMaxDrivingTimeAndDist(TimeSlot t, StaffSmartScheduleSettingDTO setting) {
        if (setting == null || setting.getDrivingRule() == null) {
            // 没有 ss 配置，不限制
            return true;
        }
        SmartScheduleDrivingRuleDTO drivingRule = setting.getDrivingRule();
        // cond1
        if (t.isBeforeAddressValid()) {
            if (t.getDriveInMinutes() <= drivingRule.getMaxTime()
                    || t.getDriveInMiles() <= drivingRule.getMaxDistInMile()) {
                return true;
            }
        }
        // cond2
        if (t.isAfterAddressValid()) {
            if (t.getDriveOutMinutes() <= drivingRule.getMaxTime()
                    || t.getDriveOutMiles() <= drivingRule.getMaxDistInMile()) {
                return true;
            }
        }
        // cond3
        // widely open
        return !t.isAfterAddressValid() && !t.isBeforeAddressValid();
        // cond4
    }

    /**
     * 检查该时间段是否为一天第一个或者最后一个预约时间，如果是标记
     * <p>
     * 用以显示开车时间是从出发点到预约点，或者预约点到结束点。
     */
    private void checkFirstOrLastAppt(List<TimeSlot> step2Times) {
        for (TimeSlot t : step2Times) {
            if (t.getBeforeServiceId() < 0) {
                t.setDriveFromStartLocation(true);
            }
            if (t.getAfterServiceId() < 0) {
                t.setDriveToEndLocation(true);
            }
        }
    }

    /**
     * 时间段开始时间根据driving time延后
     * 时间段结束时间根据driving time提前
     */
    private void updateTimeWithDrivingTime(List<TimeSlot> step2Times) {
        for (TimeSlot t : step2Times) {
            t.setStart(t.getStart() + t.getDriveInMinutes());
            t.setEnd(t.getEnd() - t.getDriveOutMinutes());
        }
    }

    /**
     * 前两个为可用时间，最后一个为不可用时间，三个取交集
     *
     * @param availableTimeRanges1
     * @param availableTimeRanges2
     * @param noAvailableTimeRanges
     * @return
     */
    public static List<TimeRangeDto> filterOutNotAvailableTimeForDay(
            List<TimeRangeDto> availableTimeRanges1,
            List<TimeRangeDto> availableTimeRanges2,
            List<TimeRangeDto> noAvailableTimeRanges) {
        if (availableTimeRanges1 == null) {
            availableTimeRanges1 = new ArrayList<>();
        }
        if (availableTimeRanges2 == null) {
            availableTimeRanges2 = new ArrayList<>();
        }
        List<TimeRangeDto> returnAvailableTimes = new ArrayList<>();
        List<TimeRangeDto> tempRanges = new ArrayList<>();
        tempRanges.addAll(availableTimeRanges1);
        tempRanges.addAll(availableTimeRanges2);
        if (CollectionUtils.isEmpty(tempRanges)) {
            return returnAvailableTimes;
        }
        int minStartTime = tempRanges.get(0).getStartTime();
        int maxEndTime = tempRanges.get(0).getEndTime();
        for (TimeRangeDto timeRange : tempRanges) {
            if (minStartTime > timeRange.getStartTime()) {
                minStartTime = timeRange.getStartTime();
            }
            if (maxEndTime < timeRange.getEndTime()) {
                maxEndTime = timeRange.getEndTime();
            }
        }
        LinkedList<Integer> allAvailableTimes = new LinkedList<>();
        for (int i = minStartTime; i <= maxEndTime; i++) {
            boolean isFind = false;
            for (TimeRangeDto availableTime1 : availableTimeRanges1) {
                if (availableTime1.getStartTime() <= i && i <= availableTime1.getEndTime()) {
                    // 在第一个可用段内找到
                    isFind = true;
                    break;
                }
            }
            if (!isFind) {
                // 上个是判断可用时间，没找到就跳过当前的时间不用找了
                continue;
            }
            // 重置为faile
            isFind = false;
            for (TimeRangeDto availableTime2 : availableTimeRanges2) {
                if (availableTime2.getStartTime() <= i && i <= availableTime2.getEndTime()) {
                    // 在第二个可用段内找到
                    isFind = true;
                    break;
                }
            }
            if (!isFind) {
                // 上个是判断可用时间，没找到就跳过当前的时间不用找了
                continue;
            }
            isFind = false;
            for (TimeRangeDto noAvailableTime : noAvailableTimeRanges) {
                if (noAvailableTime.getStartTime() < i && i < noAvailableTime.getEndTime()) {
                    isFind = true;
                    break;
                }
            }
            // 上个是判断不可用时间，应该是不能找到才对，没找到，把time保存
            if (!isFind) {
                allAvailableTimes.add(i);
            }
        }
        Collections.sort(allAvailableTimes);
        if (allAvailableTimes.size() > 0) {
            int endTime = allAvailableTimes.getLast();
            int startTime = allAvailableTimes.removeFirst();
            int lastTime = startTime;
            while (allAvailableTimes.size() > 0) {
                int timeI = allAvailableTimes.removeFirst();
                if (timeI == (lastTime + 1)) {
                    lastTime = timeI;
                } else {
                    returnAvailableTimes.add(new TimeRangeDto(startTime, lastTime));
                    startTime = timeI;
                    lastTime = timeI;
                }
                if (timeI == endTime) {
                    returnAvailableTimes.add(new TimeRangeDto(startTime, endTime));
                    break;
                }
            }
        }
        return returnAvailableTimes;
    }

    // public static List<TimeRangeDto> getStaffTime(
    //         Map<Integer, Map<LocalDate, List<TimeRangeDto>>> dateStaffWorkingHours,
    //         Integer staffId,
    //         LocalDate currLocalDate) {
    //     Map<LocalDate, List<TimeRangeDto>> oneDayOfAllStaff = dateStaffWorkingHours.get(staffId);
    //     if (oneDayOfAllStaff == null) {
    //         log.warn("no working hours for all staff on {}", currLocalDate);
    //         return Collections.emptyList();
    //     }
    //     List<TimeRangeDto> staffWorkingHour = oneDayOfAllStaff.get(currLocalDate);
    //     if (staffWorkingHour == null) {
    //         log.warn("no working hours for staff {}", staffId);
    //         return Collections.emptyList();
    //     }
    //     return staffWorkingHour;
    // }

    @Nullable
    public static StaffTime getStaffTime(
            Map<String, Map<Integer, StaffTime>> staffTime, Integer staffId, LocalDate currLocalDate) {
        var oneDayOfAllStaff = staffTime.get(currLocalDate.toString());
        if (oneDayOfAllStaff == null) {
            log.warn("no working hours for all staff on {}", currLocalDate);
            return null;
        }
        return oneDayOfAllStaff.get(staffId);
    }

    /**
     * @param myServices    所有已占用时间块
     * @param staffId       指定staff
     * @param currLocalDate 当前日期
     * @param request       全局参数
     * @return
     */
    public List<TimeSlot> buildFreeTimeSlotsByStaffId(
            List<SmartScheduleGroomingDetailsDTO> myServices,
            Integer staffId,
            LocalDate currLocalDate,
            SmartScheduleParam request) {
        final int weekDay = WeekUtil.getDayOfWeek(currLocalDate);
        List<TimeSlot> result = new ArrayList<>();
        TimeRangeDto staffWorkingHour;
        List<TimeRangeDto> staffWorkingHourList;
        // LimitDto limitDto;
        List<LimitGroupDTO> limitGroups;
        StaffTime currentStaffTime =
                SmartScheduleV2Service.getStaffTime(request.getStaffTime(), staffId, currLocalDate);
        // ob working hour 不为空，并且 staff 没有开启 sync with working hour
        if (!CollectionUtils.isEmpty(request.getObStaffTime())
                && !request.getSyncWithWorkingHourStaff().contains(staffId)) {
            // 没开 sync
            // Map<Integer, List<String>> staffOverrideDateList = request.getStaffOverrideDateList();

            // if (!CollectionUtils.isEmpty(staffOverrideDateList)
            //         && !CollectionUtils.isEmpty(staffOverrideDateList.get(staffId))
            //         && staffOverrideDateList.get(staffId).contains(currLocalDate.toString())) {
            if (Objects.nonNull(currentStaffTime) && currentStaffTime.getIsOverrideDate()) {
                // 使用 override date 覆盖
                staffWorkingHourList = currentStaffTime.getTimeRange();
            } else {
                // 使用 OB 配置
                Map<Integer, StaffTime> oneDayTimes = request.getObStaffTime().get(weekDay);
                if (oneDayTimes == null || oneDayTimes.get(staffId) == null) {
                    log.warn("no working hours for all staff on {} in OB", currLocalDate);
                    return result;
                }
                staffWorkingHourList = oneDayTimes.get(staffId).getTimeRange();
                if (CollectionUtils.isEmpty(staffWorkingHourList)) {
                    log.warn("no working hours for staff {} in OB", staffId);
                    return result;
                }
                List<TimeRangeDto> staffCurrentWorkingHour =
                        Objects.nonNull(currentStaffTime) && !currentStaffTime.getIsOverrideDate()
                                ? currentStaffTime.getTimeRange()
                                : List.of();
                staffWorkingHourList = filterOutNotAvailableTimeForDay(
                        staffCurrentWorkingHour, staffWorkingHourList, Collections.emptyList());
            }
            // limitDto = getLimitDto(request.getObStaffTime(), weekDay, staffId);
            limitGroups = getLimitGroups(request.getObStaffTime(), weekDay, staffId);
            if (CollectionUtils.isEmpty(staffWorkingHourList)) {
                log.warn("no working hours for staff {} in OB", staffId);
                return result;
            }
            Integer startTime = staffWorkingHourList.stream()
                    .map(TimeRangeDto::getStartTime)
                    .min(Integer::compareTo)
                    .get();
            Integer endTime = staffWorkingHourList.stream()
                    .map(TimeRangeDto::getEndTime)
                    .max(Integer::compareTo)
                    .get();
            staffWorkingHour = new TimeRangeDto(startTime, endTime);
        } else {
            // 开了 sync
            staffWorkingHourList = Objects.nonNull(currentStaffTime) ? currentStaffTime.getTimeRange() : List.of();
            if (CollectionUtils.isEmpty(staffWorkingHourList)) {
                log.warn("no working hours for staff {}, date {}", staffId, currLocalDate);
                return result;
            }
            Integer startTime = staffWorkingHourList.stream()
                    .map(TimeRangeDto::getStartTime)
                    .min(Integer::compareTo)
                    .get();
            Integer endTime = staffWorkingHourList.stream()
                    .map(TimeRangeDto::getEndTime)
                    .max(Integer::compareTo)
                    .get();
            staffWorkingHour = new TimeRangeDto(startTime, endTime);
            // limitDto = currentStaffTime.getLimitDto();
            limitGroups = currentStaffTime.getLimitGroups();
        }

        // // pet limit
        // Map<Integer /* weekDay */, Map<Integer /* staffId */, LimitDto>> allStaffPetLimitIdMap = new HashMap<>();
        // if (!CollectionUtils.isEmpty(request.getObStaffTime())) {
        //     allStaffPetLimitIdMap = request.getObStaffTime().entrySet().stream()
        //             .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().entrySet().stream()
        //                     .collect(Collectors.toMap(Map.Entry::getKey, entry1 -> entry1.getValue()
        //                             .getLimitDto()))));
        // } else {
        //     allStaffPetLimitIdMap = onlineBookingService.getParsedStaffPetLimitIdByBusinessId(businessId);
        // }

        // var limitdto = Optional.ofNullable(allStaffPetLimitIdMap.get(weekDay))
        //         .map(map -> map.get(staffId))
        //         .orElse(null);

        // boolean noMorePetQuantity = OBPetLimitService.judgePetLimit(
        //         request.getObPetLimitFilterDTO(), staffId, currLocalDate.toString(), limitDto, null);
        var petLimitResult = OBPetLimitService.judgePetLimitAndGetPetRemainQuantity(
                request.getObPetLimitFilterDTO(),
                staffId,
                currLocalDate.toString(),
                limitGroups,
                null,
                request.getObPetLimitFilterDTO().getPetIndexSubList());
        if (Boolean.FALSE.equals(petLimitResult.getIsAllPetsAvailable())) {
            return result;
        }

        SmartScheduleGroomingDetailsDTO preService = new SmartScheduleGroomingDetailsDTO();
        preService.setEndTime((long) staffWorkingHour.getStartTime());
        preService.setGroomingId(-1);
        preService.setServiceId(-1);
        preService.setCustomerId(-1);

        for (int i = 0; i < myServices.size(); i++) {
            SmartScheduleGroomingDetailsDTO curService = myServices.get(i);
            if (preService.getEndTime() <= curService.getStartTime()) {
                if (preService.getEndTime() >= staffWorkingHour.getEndTime()
                        || curService.getStartTime() > staffWorkingHour.getEndTime()) {
                    // preService 结束后，已到达ob关门时间
                    // curService.getStartTime的开始时间在ob关门后，不能作为最后一个slot的endTime
                    break;
                }
                TimeSlot slot = TimeSlot.builder()
                        .start(Math.toIntExact(preService.getEndTime()))
                        .end(Math.toIntExact(curService.getStartTime()))
                        .beforeApptId(preService.getGroomingId())
                        .beforeServiceId(preService.getServiceId())
                        .beforeCustomerId(preService.getCustomerId())
                        .beforeApptIsBlock(preService.getIsBlock())
                        .afterApptId(curService.getGroomingId())
                        .afterServiceId(curService.getServiceId())
                        .afterCustomerId(curService.getCustomerId())
                        .afterApptIsBlock(curService.getIsBlock())
                        .build();
                result.add(slot);
                preService = curService;
            } else if (preService.getEndTime() < curService.getEndTime()) {
                preService = curService;
            }
        }

        if (preService.getEndTime() < staffWorkingHour.getEndTime()) {
            result.add(TimeSlot.builder()
                    .start(Math.toIntExact(preService.getEndTime()))
                    .end(staffWorkingHour.getEndTime())
                    .beforeApptId(preService.getGroomingId())
                    .beforeServiceId(preService.getServiceId())
                    .beforeCustomerId(preService.getCustomerId())
                    .beforeApptIsBlock(preService.getIsBlock())
                    .afterApptId(-1)
                    .afterServiceId(-1)
                    .build());
        }
        // new resultList
        List<TimeSlot> newResultList = filterNonWorkingTime(result, staffWorkingHourList);
        log.info("build staff[{}] on date [{}] TimeSlotsV2 built: {}", staffId, currLocalDate, newResultList);
        // staff ss setting
        StaffSmartScheduleSettingDTO staffSsSetting =
                request.getStaffSsSettingMap().get(staffId);
        // 查询  customer addresses for all customer；若没有地址，从相邻 slot 获取
        if (!Boolean.TRUE.equals(request.getDisableSmartScheduling())) {
            fillServiceAddressV2(newResultList, staffSsSetting, request.getCustomerAddressDtoMap());
        }
        return newResultList;
    }

    private static List<LimitGroupDTO> getLimitGroups(
            final Map<Integer, Map<Integer, StaffTime>> obStaffTime, final int weekDay, final Integer staffId) {
        return Optional.ofNullable(obStaffTime)
                .map(map -> map.get(weekDay))
                .map(map -> map.get(staffId))
                .map(StaffTime::getLimitGroups)
                .orElse(new ArrayList<LimitGroupDTO>());
    }

    // private static LimitDto getLimitDto(
    //         final Map<Integer, Map<Integer, StaffTime>> obStaffTime, final int weekDay, final Integer staffId) {
    //     return Optional.ofNullable(obStaffTime.get(weekDay))
    //             .map(map -> map.get(staffId))
    //             .map(StaffTime::getLimitDto)
    //             .orElse(null);
    // }

    private boolean checkCACD(Integer businessId) {
        ServiceAreaSettingDTO serviceAreaSetting = iBusinessServiceAreaClient.getServiceAreaSetting(businessId);
        if (serviceAreaSetting == null) {
            return false;
        }
        return Objects.equals(serviceAreaSetting.getServiceAreaEnable(), CommonConstant.ENABLE);
    }
}
