package com.moego.server.grooming.dto;

import com.moego.server.business.dto.StaffTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class BookOnlineStaffTimeDTO {
    private Integer id;
    private Integer businessId;
    private Integer staffId;
    private Byte status;
    private Long createTime;
    private Long updateTime;
    private Map<String, StaffTime> staffTimes;
    private Map<String, StaffSlot> staffSlots;

    @Data
    public static class StaffSlot {

        private Boolean isSelected;

        private List<CapacityTimeslot> timeSlot;

        private CapacityTimeslot dailyTimeSlot;

        @Data
        public static class CapacityTimeslot {

            private Integer startTime;
            private Integer capacity;
            private List<Long> limitIds;
            private LimitDto limitDto;
            private List<LimitGroupDTO> limitGroups;

            public CapacityTimeslot() {
                this.limitIds = new ArrayList<>();
                this.limitDto = new LimitDto();
                this.limitGroups = new ArrayList<>();
            }
        }
    }
}
