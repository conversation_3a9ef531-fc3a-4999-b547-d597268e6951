package com.moego.server.grooming.dto;

import java.util.List;
import lombok.Data;
import org.springframework.util.CollectionUtils;

@Data
public class LimitGroupDTO {

    private boolean onlyAcceptSelected;
    private List<ServiceLimitDto> serviceLimitList;
    private List<PetSizeLimitDto> petSizeLimitList;
    private List<PetBreedLimitDto> petBreedLimitList;

    public boolean isEmpty() {
        return CollectionUtils.isEmpty(serviceLimitList)
                && CollectionUtils.isEmpty(petBreedLimitList)
                && CollectionUtils.isEmpty(petSizeLimitList);
    }
}
