spring:
  config:
    import:
      - "aws-secretsmanager:moego/production/datasource?prefix=secret.datasource."
      - "aws-secretsmanager:moego/production/redis?prefix=secret.redis."
      - "aws-secretsmanager:moego/production/aws?prefix=secret.aws."
      - "aws-secretsmanager:moego/production/posthog?prefix=secret.posthog."
  datasource:
    url: jdbc:mysql://${secret.datasource.mysql.url.master}:${secret.datasource.mysql.port}/moe_business?useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=false
    username: ${secret.datasource.mysql.moego_server_business.username}
    password: ${secret.datasource.mysql.moego_server_business.password}
  data:
    redis:
      host: ${secret.redis.host}
      port: ${secret.redis.port}
      ssl:
        enabled: ${secret.redis.tls}
      timeout: 60000
      password: ${secret.redis.password}
      key:
        delimiter: ':'
        prefix: apiv2

springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false

business:
  register:
    inform:
      email: <EMAIL>

s3:
  asset:
    private:
      bucket: moego-private-assets-prod
    signature:
      prefix: signature/
  domain: https://moegonew.s3-us-west-2.amazonaws.com/
  image:
    public:
      bucket: moegonew
      key:
        prefix: Public/Uploads/
  public:
    domain: https://moegonew.s3-us-west-2.amazonaws.com/
    bucket: moegonew
  region: ${secret.aws.region}
  key: ${secret.aws.access_key_id}
  secret: ${secret.aws.secret_access_key}

moego:
  data-sources:
    - name: reader
      url: jdbc:mysql://${secret.datasource.mysql.url.reader}:${secret.datasource.mysql.port}/moe_business?useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=false
      username: ${secret.datasource.mysql.moego_server_business.username}
      password: ${secret.datasource.mysql.moego_server_business.password}
  grpc:
    server:
      enabled: false
  session:
    moego-pay-max-age: 86400
    moego-pay-mobile-max-age: 2592000
    sources:
      - name: customer
        cookie-name: MGSID-C
        legacy-cookie-names:
          - MGSID
        domains:
          - pattern: '^my\.moego\.pet$'
            cookie-target-domain-level: 2
        max-age: 2592000
      - name: ob
        cookie-name: MGSID-OB
        domains:
          - pattern: '^(form|booking)\.moego\.pet$'
            cookie-target-domain-level: 2
            # xxx.moego.online
          - pattern: '^[^.]+\.moego\.online$'
            cookie-target-domain-level: 3
        max-age: 2592000
      - name: mis
        cookie-name: MGSID-MIS
        domains:
          - pattern: '^mis\.moego\.pet$'
            cookie-target-domain-level: 2
        max-age: 2592000
      - name: business
        cookie-name: MGSID-B
        domains:
          - pattern: '^.*$'
            cookie-target-domain-level: 2
        max-age: 2592000
  posthog:
    api-key: ${secret.posthog.api_key}
    api-host: https://app.posthog.com
  invite-staff:
    sign-in: 'https://go.moego.pet/sign_in?inviteCode={0}'
    sign-up: 'https://go.moego.pet/sign_up?inviteCode={0}'
