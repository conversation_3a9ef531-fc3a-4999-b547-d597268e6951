spring:
  config:
    import:
      - "aws-secretsmanager:moego/staging/datasource?prefix=secret.datasource."
      - "aws-secretsmanager:moego/staging/redis?prefix=secret.redis."
      - "aws-secretsmanager:moego/staging/aws?prefix=secret.aws."
      - "aws-secretsmanager:moego/staging/posthog?prefix=secret.posthog."
  datasource:
    url: jdbc:mysql://${secret.datasource.mysql.url.master}:${secret.datasource.mysql.port}/moe_business?useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=false
    username: ${secret.datasource.mysql.moego_server_business.username}
    password: ${secret.datasource.mysql.moego_server_business.password}
  data:
    redis:
      host: ${secret.redis.host}
      port: ${secret.redis.port}
      ssl:
        enabled: ${secret.redis.tls}
      timeout: 60000
      password: ${secret.redis.password}
      key:
        delimiter: ':'
        prefix: apiv2

springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false

business:
  register:
    inform:
      email: <EMAIL>

s3:
  asset:
    private:
      bucket: moego-private-assets-test
    signature:
      prefix: signature/
  domain: https://moegonew.s3-us-west-2.amazonaws.com/
  image:
    public:
      bucket: moegonew
      key:
        prefix: Public/Uploads/
  public:
    domain: https://moegonew.s3-us-west-2.amazonaws.com/
    bucket: moegonew
  region: ${secret.aws.region}
  key: ${secret.aws.access_key_id}
  secret: ${secret.aws.secret_access_key}

moego:
  data-sources:
    - name: reader
      url: jdbc:mysql://${secret.datasource.mysql.url.reader}:${secret.datasource.mysql.port}/moe_business?useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=false
      username: ${secret.datasource.mysql.moego_server_business.username}
      password: ${secret.datasource.mysql.moego_server_business.password}
  session:
    moego-pay-max-age: 86400
    moego-pay-mobile-max-age: 2592000
    sources:
      - name: customer
        cookie-name: MGSID-C-S1
        legacy-cookie-names:
          - MGSID-S1
        domains:
          - pattern: '^my\.s1\.moego\.(pet|dev)$'
            cookie-target-domain-level: 3
        max-age: 2592000
      - name: ob
        cookie-name: MGSID-OB-S1
        domains:
          - pattern: '^(form|booking)\.s1\.moego\.(pet|dev)$'
            cookie-target-domain-level: 3
            # xxx.s1.moego.online
          - pattern: '^[^.]+\.s1\.moego\.online$'
            cookie-target-domain-level: 4
        max-age: 2592000
      - name: mis
        cookie-name: MGSID-MIS-S1
        domains:
          - pattern: '^mis\.s1\.moego\.(pet|dev)$'
            cookie-target-domain-level: 3
        max-age: 2592000
      - name: business
        cookie-name: MGSID-B-S1
        domains:
          - pattern: '^.*$'
            cookie-target-domain-level: 3
        max-age: 2592000
  posthog:
    api-key: ${secret.posthog.api_key}
    api-host: https://app.posthog.com
  invite-staff:
    sign-in: https://go.s1.moego.dev/sign_in?inviteCode={0}
    sign-up: https://go.s1.moego.dev/sign_up?inviteCode={0}
