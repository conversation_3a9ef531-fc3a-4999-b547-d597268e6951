spring:
  config:
    import:
      - "aws-secretsmanager:moego/testing/datasource?prefix=secret.datasource."
      - "aws-secretsmanager:moego/testing/redis?prefix=secret.redis."
      - "aws-secretsmanager:moego/testing/aws?prefix=secret.aws."
      - "aws-secretsmanager:moego/testing/posthog?prefix=secret.posthog."
  datasource:
    url: jdbc:mysql://${secret.datasource.mysql.url.master}:${secret.datasource.mysql.port}/moe_business?useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=false
    username: ${secret.datasource.mysql.moego_server_business.username}
    password: ${secret.datasource.mysql.moego_server_business.password}
  data:
    redis:
      host: ${secret.redis.host}
      port: ${secret.redis.port}
      ssl:
        enabled: ${secret.redis.tls}
      timeout: 60000
      password: ${secret.redis.password}
      key:
        delimiter: ':'
        prefix: apiv2

business:
  register:
    inform:
      email: <EMAIL>

s3:
  asset:
    private:
      bucket: moego-private-assets-test
    signature:
      prefix: signature/
  domain: https://moegonew.s3-us-west-2.amazonaws.com/
  image:
    public:
      bucket: moegonew
      key:
        prefix: Public/Uploads/
  public:
    domain: https://moegonew.s3-us-west-2.amazonaws.com/
    bucket: moegonew
  region: ${secret.aws.region}
  key: ${secret.aws.access_key_id}
  secret: ${secret.aws.secret_access_key}

moego:
  data-sources:
    - name: reader
      url: jdbc:mysql://${secret.datasource.mysql.url.reader}:${secret.datasource.mysql.port}/moe_business?useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=false
      username: ${secret.datasource.mysql.moego_server_business.username}
      password: ${secret.datasource.mysql.moego_server_business.password}
  session:
    moego-pay-max-age: 86400
    moego-pay-mobile-max-age: 2592000
    sources:
      - name: customer
        cookie-name: MGSID-C-T2
        legacy-cookie-names:
          - MGSID-T2
        domains:
          - pattern: '^([^.]+-grey-)?my\.t2\.moego\.(pet|dev)$'
            cookie-target-domain-level: 3
        max-age: 2592000
      - name: ob
        cookie-name: MGSID-OB-T2
        domains:
          - pattern: '^([^.]+-grey-)?(form|booking)\.t2\.moego\.(pet|dev)$'
            cookie-target-domain-level: 3
            # xxx-grey-xxx.t2.moego.online
          - pattern: '^[^.]+\.t2\.moego\.online$'
            cookie-target-domain-level: 4
        max-age: 2592000
      - name: mis
        cookie-name: MGSID-MIS-T2
        domains:
          - pattern: '^([^.]+-grey-)?mis\.t2\.moego\.(pet|dev)$'
            cookie-target-domain-level: 3
        max-age: 2592000
      - name: business
        cookie-name: MGSID-B-T2
        domains:
          - pattern: '^.*$'
            cookie-target-domain-level: 3
        max-age: 2592000
  posthog:
    api-key: ${secret.posthog.api-key}
    api-host: https://app.posthog.com
  invite-staff:
    sign-in: https://go.t2.moego.dev/sign_in?inviteCode={0}
    sign-up: https://go.t2.moego.dev/sign_up?inviteCode={0}
