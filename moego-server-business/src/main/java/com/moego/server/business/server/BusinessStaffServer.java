package com.moego.server.business.server;

import com.moego.common.dto.StaffPermissions;
import com.moego.common.dto.StaffRolePermissions;
import com.moego.common.response.ResponseResult;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.common.migrate.MigrateInfo;
import com.moego.server.business.api.IBusinessStaffServiceBase;
import com.moego.server.business.converter.StaffConverter;
import com.moego.server.business.converter.StaffMapConverter;
import com.moego.server.business.dto.DescribeStaffsDTO;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.dto.MoeWorkingDailyDTO;
import com.moego.server.business.dto.ServiceAreaInsideBatchResultV2;
import com.moego.server.business.dto.StaffAccountBusinessDto;
import com.moego.server.business.dto.StaffInfoWithNotificationDto;
import com.moego.server.business.dto.StaffWorkingRangeDto;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.business.dto.WorkingTimeAndAreaDto;
import com.moego.server.business.mapperbean.MoeBusiness;
import com.moego.server.business.mapperbean.MoeStaff;
import com.moego.server.business.params.DescribeStaffsParams;
import com.moego.server.business.params.InitCompanyIdForStaffRequest;
import com.moego.server.business.params.ServiceAreaInsideBatchRequestV2;
import com.moego.server.business.params.StaffCACDBatchRequest;
import com.moego.server.business.params.StaffIdListParams;
import com.moego.server.business.params.StaffIdParams;
import com.moego.server.business.params.WorkingDailyQueryRangeVo;
import com.moego.server.business.service.BusinessService;
import com.moego.server.business.service.CompanyService;
import com.moego.server.business.service.RoleService;
import com.moego.server.business.service.StaffOverrideDateService;
import com.moego.server.business.service.StaffService;
import com.moego.server.business.service.StaffWorkingHourService;
import com.moego.server.business.service.WorkingDailyService;
import com.moego.server.business.vo.InitCompanyIdForStaffResponse;
import com.moego.server.grooming.api.IBookOnlineAvailableStaffService;
import jakarta.annotation.Nonnull;
import jakarta.validation.Valid;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2021/8/25 2:55 PM
 */
@RestController
@Slf4j
@RequiredArgsConstructor
public class BusinessStaffServer extends IBusinessStaffServiceBase {

    private final RoleService roleService;
    private final StaffOverrideDateService staffOverrideDateService;
    private final StaffService staffService;
    private final BusinessService businessService;
    private final CompanyService companyService;
    private final StaffWorkingHourService staffWorkingHourService;
    private final WorkingDailyService workingDailyService;
    private final MigrateHelper migrateHelper;
    private final IBookOnlineAvailableStaffService bookOnlineAvailableStaffApi;

    @Override
    public Map<Integer, Map<Integer, TimeRangeDto>> getBusinessStaffWorkingHour(
            @RequestParam("businessId") Integer businessId, @RequestParam("staffIds") List<Integer> staffIds) {
        return workingDailyService.getBusinessStaffWorkingHour(businessId, staffIds);
    }

    @Override
    public Map<Integer, Map<LocalDate, List<TimeRangeDto>>> getStaffWorkingHourWithOverrideDate(
            Integer businessId, List<Integer> staffIdList, String startDate, String endDate) {
        // 获取 working hour 设置
        return staffWorkingHourService.getStaffWorkingHourWithOverrideDate(businessId, staffIdList, startDate, endDate);
    }

    @Override
    public Map<Integer, List<String>> getStaffOverrideDateList(
            Integer businessId, List<Integer> staffIdList, String startDate, String endDate) {
        // 获取 override date 设置
        return staffOverrideDateService.getStaffOverrideDateList(businessId, staffIdList, startDate, endDate);
    }

    @Override
    public Map<Integer, MoeStaffDto> getStaffNames(@RequestBody StaffIdListParams staffIdListParams) {
        return staffService.queryStaffNames(staffIdListParams);
    }

    @Override
    public Integer getOwnerStaffId(@Nonnull Integer businessId) {
        return staffService.getOwnerStaffId(businessId);
    }

    @Override
    public ServiceAreaInsideBatchResultV2 isLocationInsideAreaBatchV2(
            @RequestParam("businessId") Integer businessId,
            @Valid @RequestBody ServiceAreaInsideBatchRequestV2 request) {
        return staffService.isLocationInsideAreaBatchV2(businessId, request);
    }

    @Override
    public Map<Long, Map<String, Map<Integer, List<WorkingTimeAndAreaDto>>>> queryStaffCACDBatch(
            @RequestBody StaffCACDBatchRequest request) {
        return staffService.queryStaffCACDBatch(request);
    }

    @Override
    public MoeStaffDto getStaff(@RequestBody StaffIdParams staffIdParams) {
        if (staffIdParams.getStaffId() == null || staffIdParams.getStaffId() <= 0) {
            return null;
        }
        MoeStaff moeStaff = staffService.getStaffById(staffIdParams.getStaffId());
        if (moeStaff == null) {
            return null;
        }
        if (staffIdParams.getBusinessId() != null) {
            var company = companyService.getCompanyByBusinessId(staffIdParams.getBusinessId());
            if (company == null) {
                return null;
            }
            if (moeStaff.getEnterpriseId() > 0) {
                if (!moeStaff.getEnterpriseId().equals(company.getEnterpriseId())) {
                    return null;
                }
                moeStaff.setCompanyId(company.getId());
            } else {
                var isMigrate = migrateHelper.isMigrate(company.getId().longValue());
                if (isMigrate) {
                    if (!moeStaff.getCompanyId().equals(company.getId())) {
                        return null;
                    }
                } else {
                    if (!moeStaff.getBusinessId().equals(staffIdParams.getBusinessId())) {
                        return null;
                    }
                }
            }
        }
        return StaffMapConverter.INSTANCE.toStaffDTO(moeStaff);
    }

    @Override
    public StaffAccountBusinessDto getStaffWithAccount(@RequestBody StaffIdParams staffIdParams) {
        if (staffIdParams.getStaffId() == null) {
            return new StaffAccountBusinessDto();
        }
        return staffService.getStaffWithAccount(staffIdParams);
    }

    @Override
    public List<MoeStaffDto> getStaffList(@RequestBody StaffIdListParams staffIdListParams) {
        return staffService.queryStaffByIdList(staffIdListParams);
    }

    @Override
    public List<MoeStaffDto> getStaffListV2(@RequestBody StaffIdListParams staffIdListParams) {
        return staffService.queryStaffByIdListV2(staffIdListParams);
    }

    @Override
    public List<MoeStaffDto> getStaffListForCalendar(
            @RequestParam("businessId") Integer businessId, @RequestParam("staffId") Integer staffId) {
        return staffService.getStaffListForCalendar(businessId, staffId);
    }

    @Override
    public List<MoeStaffDto> getStaffListByBusinessId(Integer businessId, Boolean withDeleted) {
        List<MoeStaffDto> staffDtoList = new ArrayList<>();
        MigrateInfo migrateInfo = migrateHelper.getMigrationInfo(businessId);
        List<MoeStaff> moeStaffs = staffService.getStaffListByBusinessId(businessId, withDeleted, migrateInfo);
        moeStaffs.forEach(staff -> {
            MoeStaffDto moeStaffDto = new MoeStaffDto();
            BeanUtils.copyProperties(staff, moeStaffDto);
            staffDtoList.add(moeStaffDto);
        });
        return staffDtoList;
    }

    @Override
    public ResponseResult<List<StaffWorkingRangeDto>> queryRange(
            Integer businessId, Integer staffId, @RequestBody WorkingDailyQueryRangeVo rangeVo) {
        return ResponseResult.success(workingDailyService.simpleQueryStaffWorkTime(businessId, 0, rangeVo));
    }

    @Override
    public Map<Integer, Map<String, List<TimeRangeDto>>> queryStaffWorkingHourRange(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("staffIdList") List<Integer> staffIdList,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate) {
        return workingDailyService.queryStaffWorkTimeByRange(businessId, staffIdList, startDate, endDate);
    }

    @Override
    public List<Integer> showCalenderStaff(
            @RequestParam("businessId") Integer businessId, @RequestParam("staffId") Integer staffId) {
        return staffService.getStaffListForCalendar(businessId, staffId).stream()
                .map(MoeStaffDto::getId)
                .toList();
    }

    @Override
    public ResponseResult<List<MoeWorkingDailyDTO>> query(
            @RequestParam("businessId") Integer businessId, @RequestBody MoeWorkingDailyDTO moeWorkingDailyDTO) {
        moeWorkingDailyDTO.setBusinessId(businessId);
        return ResponseResult.success(workingDailyService.query(moeWorkingDailyDTO));
    }

    @Override
    public StaffPermissions getBusinessRoleByStaffId(@RequestParam("staffId") Integer staffId) {
        return roleService.getStaffPermissionIdsStr(staffId);
    }

    @Override
    public Map<Integer, StaffInfoWithNotificationDto> queryStaffByIdList(
            @RequestParam("businessId") Integer businessId) {
        return staffService.getStaffSettingList(businessId);
    }

    @Override
    public List<MoeStaffDto> queryBookOnlineAvailableStaff(@RequestParam("businessId") Integer businessId) {
        return bookOnlineAvailableStaffApi.getAvailableStaffListInAvailabilityType(businessId);
    }

    @Override
    public void updateStaff(@RequestBody MoeStaffDto moeStaffDto) {
        staffService.updateStaffInternal(moeStaffDto);
    }

    @Override
    public DescribeStaffsDTO describeStaffs(DescribeStaffsParams params) {
        var data = staffService.describeStaffs(params, params.pagination());
        return new DescribeStaffsDTO(
                data.getFirst().stream()
                        .map(StaffConverter.INSTANCE::entityToDTO)
                        .toList(),
                data.getSecond());
    }

    @Override
    public MoeStaffDto createStaff(MoeStaffDto staff) {
        staff.setId(null);
        var entity = new MoeStaff();
        BeanUtils.copyProperties(staff, entity);
        var business = businessService.getBusinessInfo(staff.getBusinessId());
        entity.setCompanyId(business.getCompanyId());
        staffService.insert(entity);
        var result = new MoeStaffDto();
        BeanUtils.copyProperties(entity, result);
        return result;
    }

    @Override
    public InitCompanyIdForStaffResponse initCompanyIdForStaff(InitCompanyIdForStaffRequest params) {
        int companyId = params.startCompanyId();
        while (companyId < params.endCompanyId()) {
            // 查询 company 下的所有 business id
            var businesses = businessService.getCompanyBusinessList(companyId);
            var businessIds = businesses.stream().map(MoeBusiness::getId).collect(Collectors.toSet());

            // 删除的 business 会把 company id 变成负数，也查出来
            var deletedBusinesses = businessService.getCompanyBusinessList(-companyId);
            var deletedBusinessIds =
                    deletedBusinesses.stream().map(MoeBusiness::getId).collect(Collectors.toSet());

            // 将这些 business 下的所有 staff 的 company id 设置为当前 company id
            var count = staffService.initCompanyId(companyId, businessIds);
            var deletedCount = staffService.initCompanyId(companyId, deletedBusinessIds);

            log.info(
                    "init company id {}, business count {}, deleted business count {}, staff count {}, deleted staff count {}",
                    companyId,
                    businessIds.size(),
                    deletedBusinessIds.size(),
                    count,
                    deletedCount);

            var nextCompanyId = companyService.queryNextCompanyId(companyId);
            if (nextCompanyId == null) {
                break;
            }
            companyId = nextCompanyId;
        }

        return new InitCompanyIdForStaffResponse();
    }

    @Override
    public List<StaffRolePermissions> getStaffRolePermissionsByBusinessIds(List<Integer> businessIds) {
        return roleService.getStaffRolePermissionsByBusinessIds(businessIds);
    }
}
