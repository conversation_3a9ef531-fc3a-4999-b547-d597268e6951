package com.moego.server.business.web;

import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.business.dto.StaffWorkingAreaDetailDTO;
import com.moego.server.business.dto.StaffWorkingHourDetailDTO;
import com.moego.server.business.service.StaffWorkingAreaService;
import com.moego.server.business.service.StaffWorkingHourService;
import com.moego.server.business.service.WorkingAreaService;
import com.moego.server.business.web.request.SyncWorkingAreaScheduleTypeRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/business/staff/working/area")
public class StaffWorkingAreaController {

    @Autowired
    private StaffWorkingAreaService staffWorkingAreaService;

    @Autowired
    private WorkingAreaService workingAreaService;

    @Autowired
    private StaffWorkingHourService workingHourService;

    @GetMapping
    @Auth(AuthType.BUSINESS)
    public StaffWorkingAreaDetailDTO getStaffWorkingArea(
            AuthContext context, @RequestParam("staffId") Integer staffId) {
        StaffWorkingAreaDetailDTO staffWorkingAreaDetailDTO =
                staffWorkingAreaService.getStaffWorkingAreaDetailWithDefault(context.getBusinessId(), staffId);
        workingAreaService.areaIdDeleteCheck(
                context.getBusinessId(), staffWorkingAreaService.getWorkingAreas(staffWorkingAreaDetailDTO));
        return staffWorkingAreaDetailDTO;
    }

    @PutMapping
    @Auth(AuthType.BUSINESS)
    public void saveStaffWorkingArea(
            AuthContext context, @RequestBody @Valid StaffWorkingAreaDetailDTO staffWorkingAreaDetailDTO) {
        staffWorkingAreaDetailDTO.setBusinessId(context.getBusinessId());
        staffWorkingAreaService.saveStaffWorkingArea(context.getCompanyId(), staffWorkingAreaDetailDTO);
    }

    @PutMapping("/syncScheduleType")
    @Auth(AuthType.BUSINESS)
    // app 改了 working hour 周期后也要同步修改 service area 周期
    public void syncScheduleType(AuthContext context, @RequestBody @Valid SyncWorkingAreaScheduleTypeRequest request) {
        StaffWorkingHourDetailDTO workingHourDetailDTO = workingHourService.getStaffWorkingHourDetail(
                context.companyId(), context.getBusinessId(), request.staffId());
        staffWorkingAreaService.syncScheduleType(
                context.companyId(),
                context.getBusinessId(),
                request.staffId(),
                workingHourDetailDTO.getScheduleType());
    }
}
