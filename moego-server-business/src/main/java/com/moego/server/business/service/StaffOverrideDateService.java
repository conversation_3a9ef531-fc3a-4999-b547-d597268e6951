package com.moego.server.business.service;

import com.moego.common.utils.DateUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.business.dto.StaffOverrideDateDetailDTO;
import com.moego.server.business.dto.StaffWorkingRangeDto;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.business.mapper.MoeStaffOverrideDateMapper;
import com.moego.server.business.mapperbean.MoeStaffOverrideDate;
import com.moego.server.business.web.vo.StaffOverrideDateResponse;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class StaffOverrideDateService {

    @Autowired
    private MoeStaffOverrideDateMapper moeStaffOverrideDateMapper;

    @Autowired
    private StaffService staffService;

    @Autowired
    private MigrateHelper migrateHelper;

    public StaffOverrideDateResponse getStaffOverrideDate(Integer businessId, Integer staffId) {
        LocalDate today = LocalDate.now();
        Map<Boolean, List<StaffOverrideDateDetailDTO>> collect =
                moeStaffOverrideDateMapper.selectByBusinessIdAndStaffId(businessId, staffId).stream()
                        .map(StaffOverrideDateService::getOverrideDateDetailDTO)
                        .collect(Collectors.partitioningBy(staffOverrideDate ->
                                staffOverrideDate.getOverrideDate().isBefore(today)));

        StaffOverrideDateResponse response = new StaffOverrideDateResponse();
        response.setHistory(collect.get(Boolean.TRUE));
        response.setOnGoing(collect.get(Boolean.FALSE));
        return response;
    }

    private static StaffOverrideDateDetailDTO getOverrideDateDetailDTO(MoeStaffOverrideDate staffOverrideDate) {
        StaffOverrideDateDetailDTO staffOverrideDateDetailDTO = new StaffOverrideDateDetailDTO();
        BeanUtils.copyProperties(staffOverrideDate, staffOverrideDateDetailDTO);
        staffOverrideDateDetailDTO.setOverrideDate(LocalDate.parse(staffOverrideDate.getOverrideDate()));
        staffOverrideDateDetailDTO.setTimeData(JsonUtil.toList(staffOverrideDate.getTimeData(), TimeRangeDto.class));
        return staffOverrideDateDetailDTO;
    }

    public void saveStaffOverrideDate(
            Long companyId, Integer businessId, List<StaffOverrideDateDetailDTO> staffOverrideDateDetailDTOList) {
        List<MoeStaffOverrideDate> list = staffOverrideDateDetailDTOList.stream()
                .map(staffOverrideDateDTO -> {
                    MoeStaffOverrideDate staffOverrideDate = new MoeStaffOverrideDate();
                    staffOverrideDate.setStaffId(staffOverrideDateDTO.getStaffId());
                    staffOverrideDate.setCompanyId(companyId);
                    staffOverrideDate.setBusinessId(businessId);
                    staffOverrideDate.setOverrideDate(
                            staffOverrideDateDTO.getOverrideDate().toString());
                    staffOverrideDate.setTimeData(JsonUtil.toJson(staffOverrideDateDTO.getTimeData()));
                    return staffOverrideDate;
                })
                .collect(Collectors.toList());
        moeStaffOverrideDateMapper.batchInsertOrUpdate(list);
    }

    public void deleteStaffOverrideDate(Integer businessId, Integer id) {
        moeStaffOverrideDateMapper.deleteByPrimaryKeyAndBusinessId(businessId, id);
    }

    /**
     * 获取公司正常状态员工在一段日期范围内的 override date
     * @return
     */
    public List<StaffWorkingRangeDto> queryRangeShiftManagementOverrideDate(
            Integer businessId, String startDateRequest, String endDateRequest) {
        // 参数检查
        if (!StringUtils.hasText(endDateRequest) || !StringUtils.hasText(startDateRequest)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "date is null or empty");
        }
        startDateRequest = DateUtil.convertToDateString(startDateRequest);
        endDateRequest = DateUtil.convertToDateString(endDateRequest);

        // 查询状态正常的员工列表
        Set<Integer> staffIds = new HashSet<>(staffService.getCurrentWorkingLocationStaffIds(businessId));
        if (CollectionUtils.isEmpty(staffIds)) {
            return Collections.emptyList();
        }

        // 获取 override 设置
        Map<Pair<Integer, LocalDate>, List<TimeRangeDto>> staffOverrideDetailMap =
                getStaffOverrideDateDetailMap(businessId, startDateRequest, endDateRequest);
        Map<Integer, StaffWorkingRangeDto> result = new HashMap<>();
        staffOverrideDetailMap.forEach((key, timeRangeDtoList) -> {
            Integer staffId = key.getLeft();
            if (!staffIds.contains(staffId)) {
                return;
            }
            LocalDate overrideDate = key.getRight();
            StaffWorkingRangeDto dto;
            if (result.containsKey(staffId)) {
                dto = result.get(staffId);
                dto.getTimeRange().put(overrideDate.toString(), timeRangeDtoList);
            } else {
                dto = new StaffWorkingRangeDto();
                dto.setStaffId(staffId);
                dto.setTimeRange(new HashMap<>() {
                    {
                        put(overrideDate.toString(), timeRangeDtoList);
                    }
                });
                result.put(staffId, dto);
            }
        });
        return new ArrayList<>(result.values());
    }

    /**
     * 获取商家一段时间内员工的 override date 设置
     *
     * @param businessId
     * @param startDate
     * @param endDate
     * @return
     */
    public Map<Pair<Integer, LocalDate>, List<TimeRangeDto>> getStaffOverrideDateDetailMap(
            Integer businessId, String startDate, String endDate) {
        return moeStaffOverrideDateMapper.selectByBusinessIdAndDate(businessId, startDate, endDate).stream()
                .map(StaffOverrideDateService::getStaffOverrideDateDetailDTO)
                .collect(Collectors.toMap(
                        k -> Pair.of(k.getStaffId(), k.getOverrideDate()),
                        StaffOverrideDateDetailDTO::getTimeData,
                        (v1, v2) -> v2));
    }

    private static StaffOverrideDateDetailDTO getStaffOverrideDateDetailDTO(MoeStaffOverrideDate staffOverrideDate) {
        StaffOverrideDateDetailDTO staffOverrideDateDetailDTO = new StaffOverrideDateDetailDTO();
        Integer staffId = staffOverrideDate.getStaffId();
        staffOverrideDateDetailDTO.setStaffId(staffId);
        staffOverrideDateDetailDTO.setOverrideDate(LocalDate.parse(staffOverrideDate.getOverrideDate()));
        List<TimeRangeDto> timeData = JsonUtil.toList(staffOverrideDate.getTimeData(), TimeRangeDto.class);
        staffOverrideDateDetailDTO.setTimeData(timeData);
        return staffOverrideDateDetailDTO;
    }

    /**
     * 获取商家一段时间内员工的 override date 设置
     *
     * @param businessId
     * @param staffIdList
     * @param startDate
     * @param endDate
     * @return
     */
    public Map<Integer, List<String>> getStaffOverrideDateList(
            Integer businessId, List<Integer> staffIdList, String startDate, String endDate) {
        return moeStaffOverrideDateMapper
                .selectByBusinessIdAndStaffIdAndDate(businessId, staffIdList, startDate, endDate)
                .stream()
                .collect(Collectors.groupingBy(
                        MoeStaffOverrideDate::getStaffId,
                        Collectors.mapping(MoeStaffOverrideDate::getOverrideDate, Collectors.toList())));
    }
}
