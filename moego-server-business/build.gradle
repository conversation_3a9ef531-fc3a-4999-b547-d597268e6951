plugins {
    id 'org.springframework.boot'
    id "com.qqviaja.gradle.MybatisGenerator" version "${mybatisGeneratorGradlePlugin}"
}

dependencies {

    implementation("com.moego:moego-server-common")
    implementation("com.moego:moego-server-api")
    implementation("com.moego.api:moego-api-java")
    implementation("com.moego.lib:moego-lib-common")
    implementation("com.moego.lib:moego-lib-springdoc")
    implementation("com.moego.lib:moego-lib-aws")
    implementation("com.moego.lib:moego-lib-permission")

    implementation(platform("software.amazon.awssdk:bom:${awsSdkVersion}"))

    implementation 'org.springframework.boot:spring-boot-starter-freemarker'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-jdbc'
    implementation 'org.springframework.boot:spring-boot-starter-cache'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
    implementation("io.awspring.cloud:spring-cloud-aws-starter-secrets-manager:${springCloudAWSVersion}")
    implementation("io.grpc:grpc-services:${grpcVersion}")
    implementation("org.mybatis.spring.boot:mybatis-spring-boot-starter:${mybatisBootStarterVersion}")
    implementation("com.github.pagehelper:pagehelper-spring-boot-starter:${pagehelperSpringBootStarterVersion}")
    implementation 'org.hibernate.orm:hibernate-core'
    implementation 'software.amazon.awssdk:s3'
    implementation 'com.mandrillapp.wrapper.lutung:lutung:0.0.8'
    implementation 'net.sf.jmimemagic:jmimemagic:0.1.5'
    // easy excel
    implementation 'com.alibaba:easyexcel:3.2.1'

    compileOnly 'com.github.spotbugs:spotbugs-annotations:4.7.3'

    //PostHog
    implementation 'com.posthog.java:posthog:+'
    // map struct
    implementation("org.mapstruct:mapstruct:${mapstructVersion}")
    annotationProcessor("org.mapstruct:mapstruct-processor:${mapstructVersion}")
    testAnnotationProcessor("org.mapstruct:mapstruct-processor:${mapstructVersion}")
    annotationProcessor 'no.entur.mapstruct.spi:protobuf-spi-impl:1.43'
    testAnnotationProcessor 'no.entur.mapstruct.spi:protobuf-spi-impl:1.43'

    runtimeOnly 'com.mysql:mysql-connector-j'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok-mapstruct-binding:0.2.0'
    testCompileOnly 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok'
}

sourceSets.main.resources.srcDirs = ['src/main/java', 'src/main/resources']

bootJar {
  archivesBaseName = 'moego-server'
  version = ''
}
bootRun {
  jvmArgs "--add-opens", "java.base/sun.net=ALL-UNNAMED"
}

configurations {
    mybatisGenerator
}

mybatisGenerator {
    verbose = true
    configFile = 'moego-server-business/src/main/resources/MyBatisGeneratorConfig.xml'

    dependencies {
        mybatisGenerator("org.mybatis.generator:mybatis-generator-core:${mybatisGeneratorCoreVersion}")
        mybatisGenerator("com.mysql:mysql-connector-j")
        mybatisGenerator("com.moego.lib:moego-lib-mybatis-plugins")
    }
}

// mbGenerator needs moego-lib-mybatis-plugins.jar, so we need to make sure it is built before mbGenerator
mbGenerator.dependsOn gradle.includedBuild("moego-java-lib").task(":moego-lib-mybatis-plugins:jar")
