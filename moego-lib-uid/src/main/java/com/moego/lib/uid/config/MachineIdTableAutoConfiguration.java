package com.moego.lib.uid.config;

import com.zaxxer.hikari.HikariDataSource;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.Map;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import me.ahoo.cosid.jdbc.JdbcMachineIdDistributor;
import me.ahoo.cosid.spring.boot.starter.machine.CosIdJdbcMachineIdDistributorAutoConfiguration;
import me.ahoo.cosid.spring.boot.starter.machine.CosIdMachineAutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.io.ClassPathResource;

/**
 * 自动初始化 machine id 分配记录表
 */
@Slf4j
@AutoConfiguration(
        // 在成功装配 jdbcMachineIdDistributor 后执行
        after = CosIdJdbcMachineIdDistributorAutoConfiguration.class,
        // 在请求分配 machine id 之前执行
        before = CosIdMachineAutoConfiguration.class)
@ConditionalOnBean(JdbcMachineIdDistributor.class)
@ConditionalOnProperty(prefix = "cosid.jdbc", name = "auto-init", havingValue = "true")
public class MachineIdTableAutoConfiguration {

    private static final Map<String, String> SCHEMA_PATHS = Map.of(
            // MySQL
            "com.mysql.cj.jdbc.Driver", "sql/schema-mysql.sql",
            "com.mysql.jdbc.Driver", "sql/schema-mysql.sql",
            // PostgreSQL
            "org.postgresql.Driver", "sql/schema-postgresql.sql",
            // H2, for unit test
            "org.h2.Driver", "sql/schema-h2.sql");

    @SneakyThrows
    public MachineIdTableAutoConfiguration(HikariDataSource dataSource) {
        var driverClassName = dataSource.getDriverClassName();
        var schemaPath = SCHEMA_PATHS.get(driverClassName);
        if (schemaPath == null) {
            throw new IllegalArgumentException("unsupported driver class name: " + driverClassName);
        }
        ClassPathResource resource = new ClassPathResource(schemaPath);
        var sql = resource.getContentAsString(StandardCharsets.UTF_8);
        try (Connection connection = dataSource.getConnection();
                PreparedStatement ps = connection.prepareStatement(sql)) {
            ps.execute();
        }
        log.info("Initialize schema successfully, driver: %s, schema path: %s".formatted(driverClassName, schemaPath));
    }
}
